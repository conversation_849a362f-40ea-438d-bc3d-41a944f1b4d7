# RoboTwin AutoDL 环境使用指南

## 📋 项目概述

RoboTwin是一个双臂机器人协作挑战项目，专为CVPR 2025比赛设计。项目包含6个不同的机器人任务，支持多种基线策略（ACT、DP、RDT等）。

## ✅ 环境状态

### 已完成的配置
- ✅ **Python环境**: Python 3.10.16
- ✅ **核心依赖**: SAPIEN、PyTorch、cuRobo等
- ✅ **数据集**: 1000+背景纹理，28类杂乱物体
- ✅ **机器人模型**: aloha-agilex-1, franka-panda
- ✅ **任务模块**: 6个完整任务

### 系统信息
- **操作系统**: Ubuntu (AutoDL服务器)
- **GPU**: NVIDIA GPU (支持CUDA 12.1)
- **Python版本**: 3.10.16
- **PyTorch版本**: 2.4.1+cu121

## 🚀 快速开始

### 1. 激活环境
```bash
cd /root/autodl-tmp/RoboTwin
conda activate RoboTwin_Challenge
```

### 2. 运行基本测试
```bash
python simple_test.py
```

### 3. 运行任务示例
```bash
# 运行物体放置任务
bash run_task.sh place_object_scale 0

# 或者直接使用Python
echo "place_object_scale" | python script/run_task.py
```

## 📁 项目结构

```
RoboTwin/
├── assets/                    # 资源文件
│   ├── background_texture/    # 背景纹理 (1200张)
│   ├── embodiments/          # 机器人模型
│   ├── messy_objects/        # 杂乱物体 (28类)
│   └── objects/              # 任务物体
├── envs/                     # 环境定义
│   ├── place_object_scale.py # 物体放置任务
│   ├── blocks_ranking_rgb.py # 积木排序任务
│   └── ...                  # 其他任务
├── policy/                   # 策略实现
│   ├── ACT/                 # Action Chunking Transformer
│   ├── DP/                  # Diffusion Policy
│   ├── RDT/                 # Robotic Diffusion Transformer
│   └── Your_Policy/         # 自定义策略
├── script/                   # 运行脚本
├── task_config/             # 任务配置
└── third_party/             # 第三方库
```

## 🎯 可用任务

1. **place_object_scale** - 物体放置到电子秤
2. **blocks_ranking_rgb** - 按颜色排序积木
3. **blocks_stack_three** - 堆叠三个积木
4. **dual_shoes_place** - 双臂放置鞋子
5. **place_phone_stand** - 放置手机支架
6. **put_bottles_dustbin** - 将瓶子放入垃圾桶

## 🔧 常用命令

### 运行任务
```bash
# 基本运行
bash run_task.sh <task_name> <gpu_id>

# 示例
bash run_task.sh place_object_scale 0
bash run_task.sh blocks_stack_three 0
```

### 策略训练
```bash
# ACT策略
cd policy/ACT
bash train.sh

# Diffusion Policy
cd policy/DP
bash train.sh

# RDT策略
cd policy/RDT
bash pretrain.sh
```

### 策略评估
```bash
# 评估训练好的策略
python script/eval_policy.py --policy_path <path_to_policy>
```

## 📊 数据收集

### 收集演示数据
```bash
# 修改任务配置文件
vim task_config/place_object_scale.yml

# 设置collect_data: true
# 运行数据收集
bash run_task.sh place_object_scale 0
```

### 数据格式
- **RGB图像**: 头部和手腕相机
- **深度图像**: 可选
- **点云数据**: 下采样到1024点
- **关节状态**: 机器人关节角度
- **末端位姿**: 机器人末端执行器位置

## ⚙️ 配置说明

### 任务配置 (task_config/*.yml)
```yaml
task_name: place_object_scale
episode_num: 100
render_freq: 10
embodiment: ["aloha-agilex-1"]
augmentation:
  random_background: true
  messy_table: false
  random_light: false
camera:
  head_camera_type: "D435"
  wrist_camera_type: "D435"
```

### 机器人配置
- **aloha-agilex-1**: 双臂移动机器人
- **franka-panda**: 双臂桌面机器人

## 🐛 故障排除

### 常见问题

1. **渲染错误**
```bash
# 如果遇到Vulkan渲染问题，设置无头模式
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 &
```

2. **内存不足**
```bash
# 减少并行进程数或降低图像分辨率
# 修改task_config中的相关参数
```

3. **CUDA错误**
```bash
# 检查GPU状态
nvidia-smi
# 设置CUDA设备
export CUDA_VISIBLE_DEVICES=0
```

## 📈 性能优化

### 训练加速
- 使用混合精度训练
- 调整batch size
- 使用数据并行

### 推理优化
- 模型量化
- TensorRT优化
- 批处理推理

## 🔗 相关资源

- **项目主页**: [RoboTwin GitHub](https://github.com/TianxingChen/RoboTwin)
- **CVPR 2025 Challenge**: 双臂机器人协作挑战
- **SAPIEN文档**: 物理仿真引擎
- **cuRobo文档**: GPU加速运动规划

## 📞 技术支持

如遇到问题，请：
1. 首先运行 `python simple_test.py` 检查环境
2. 查看日志文件了解详细错误信息
3. 参考项目README和文档
4. 在GitHub Issues中搜索相关问题

---

**环境配置完成时间**: $(date)
**配置状态**: ✅ 完全可用
**测试结果**: 5/5 通过
