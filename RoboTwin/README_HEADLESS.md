# RoboTwin 无头模式快速指南

## 🚀 快速开始

### 1. 一键设置环境
```bash
# 运行自动设置脚本
bash setup_headless.sh
```

### 2. 运行任务
```bash
# 基本用法
bash run_task_headless.sh <任务名称> <GPU编号>

# 示例
bash run_task_headless.sh place_object_scale 0
```

## 📋 支持的任务

- `place_object_scale` - 将物体放置到电子秤上
- `pick_and_place` - 拾取和放置任务
- `stack_objects` - 堆叠物体任务
- 更多任务请查看 `task_config/` 目录

## 🔧 手动设置（如果自动设置失败）

### 1. 安装系统依赖
```bash
sudo apt-get update
sudo apt-get install -y xvfb libgl1-mesa-glx gettext-base
```

### 2. 设置Conda环境
```bash
# 创建环境
conda env create -f environment.yml
conda activate RoboTwin_Challenge

# 或手动创建
conda create -n RoboTwin_Challenge python=3.10 -y
conda activate RoboTwin_Challenge
pip install -r requirements.txt
```

### 3. 设置虚拟显示
```bash
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
```

### 4. 更新配置文件
```bash
bash .update_path.sh
```

## 📊 监控和调试

### 查看GPU状态
```bash
nvidia-smi
watch -n 1 nvidia-smi  # 实时监控
```

### 查看任务日志
```bash
# 保存日志到文件
bash run_task_headless.sh place_object_scale 0 > task.log 2>&1

# 实时查看日志
bash run_task_headless.sh place_object_scale 0 | tee task.log
```

### 后台运行
```bash
# 后台运行任务
nohup bash run_task_headless.sh place_object_scale 0 > task.log 2>&1 &

# 查看后台任务
jobs
ps aux | grep python
```

## ❗ 常见问题

### 1. 环境问题
```bash
# 检查conda环境
conda env list

# 重新激活环境
conda activate RoboTwin_Challenge
```

### 2. 显示问题
```bash
# 检查虚拟显示
ps aux | grep Xvfb
echo $DISPLAY

# 重启虚拟显示
pkill Xvfb
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
export DISPLAY=:99
```

### 3. 配置文件问题
```bash
# 重新生成配置
bash .update_path.sh

# 检查配置文件
cat assets/embodiments/aloha-agilex-1/curobo_left.yml
```

### 4. 模型文件问题
```bash
# 下载完整资源
cd assets
python _download.py

# 或创建基础模型（临时解决）
mkdir -p assets/objects/050_bell
echo '{"scale": [0.001, 0.001, 0.001], "transform_matrix": [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]], "contact_points_pose": [[0, 0, 0.05, 1, 0, 0, 0]], "functional_points_pose": [[0, 0, 0, 1, 0, 0, 0]]}' > assets/objects/050_bell/model_data0.json
```

## 📚 详细文档

- [无头模式脚本使用教程](docs/无头模式脚本使用教程.md)
- [故障排除指南](docs/故障排除指南.md)

## 🎯 性能优化建议

1. **GPU内存**: 确保有足够的GPU内存（建议8GB+）
2. **系统内存**: 建议16GB+系统内存
3. **存储空间**: 确保有足够的磁盘空间存储模型和日志
4. **网络**: 稳定的网络连接用于下载资源

## 📞 获取帮助

如果遇到问题：
1. 查看错误日志
2. 参考故障排除指南
3. 检查GitHub Issues
4. 联系技术支持

---

**提示**: 首次运行可能需要下载大量模型文件，请确保网络连接稳定。
