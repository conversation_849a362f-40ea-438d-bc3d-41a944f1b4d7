<?xml version='1.0' encoding='utf-8'?>
<robot name="model.urdf">
  <link name="baseLink">
    <contact>
      <friction_anchor />
      <lateral_friction value="0.3" />
      <rolling_friction value="0.0" />
      <contact_cfm value="0.0" />
      <contact_erp value="1.0" />
    </contact>
    <inertial>
       <origin rpy="0 0 0" xyz="0 0 0" />
       <mass value="0.1" />
       <inertia ixx="8e-05" ixy="0" ixz="0" iyy="0.0002" iyz="0" izz="0.0002" />
    </inertial>
    <visual>
      <geometry>
        <mesh filename="textured.obj" scale="0.001 0.001 0.001" />
      </geometry>
      
    </visual>
    <collision>
      <geometry>
    	 	<mesh filename="coacd_collision.obj" scale="0.001 0.001 0.001" />
      </geometry>
    </collision>
  </link>
</robot>