#!/usr/bin/env python3
"""
RoboTwin 数据质量检查脚本
作者: Augment Agent
用途: 检查生成的训练数据的质量和完整性
"""

import os
import sys
import h5py
import json
import numpy as np
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
from collections import defaultdict
import cv2

class DataQualityChecker:
    def __init__(self, data_path, task_name):
        self.data_path = Path(data_path)
        self.task_name = task_name
        self.stats = defaultdict(list)
        self.issues = []
        
    def check_file_integrity(self):
        """检查文件完整性"""
        print("🔍 检查文件完整性...")
        
        # 检查HDF5文件
        hdf5_files = list(self.data_path.glob("episode*.hdf5"))
        if not hdf5_files:
            self.issues.append("❌ 未找到HDF5数据文件")
            return False
            
        print(f"✅ 找到 {len(hdf5_files)} 个HDF5文件")
        
        # 检查文件连续性
        expected_files = set(f"episode{i}.hdf5" for i in range(len(hdf5_files)))
        actual_files = set(f.name for f in hdf5_files)
        
        missing_files = expected_files - actual_files
        if missing_files:
            self.issues.append(f"❌ 缺少文件: {missing_files}")
        
        # 检查每个文件是否可读
        corrupted_files = []
        for hdf5_file in hdf5_files:
            try:
                with h5py.File(hdf5_file, 'r') as f:
                    # 尝试读取基本结构
                    if 'observation' not in f:
                        corrupted_files.append(hdf5_file.name)
            except Exception as e:
                corrupted_files.append(f"{hdf5_file.name}: {str(e)}")
        
        if corrupted_files:
            self.issues.append(f"❌ 损坏的文件: {corrupted_files}")
        else:
            print("✅ 所有文件完整且可读")
            
        return len(corrupted_files) == 0
    
    def analyze_data_structure(self):
        """分析数据结构"""
        print("\n📊 分析数据结构...")
        
        hdf5_files = list(self.data_path.glob("episode*.hdf5"))
        if not hdf5_files:
            return
        
        # 分析第一个文件的结构
        sample_file = hdf5_files[0]
        try:
            with h5py.File(sample_file, 'r') as f:
                print(f"📁 数据结构 (基于 {sample_file.name}):")
                self._print_hdf5_structure(f, indent="  ")
                
                # 检查必要的数据字段
                required_fields = [
                    'observation',
                    'joint_action'
                ]
                
                for field in required_fields:
                    if field not in f:
                        self.issues.append(f"❌ 缺少必要字段: {field}")
                    else:
                        print(f"✅ 包含字段: {field}")
                        
        except Exception as e:
            self.issues.append(f"❌ 无法分析数据结构: {str(e)}")
    
    def _print_hdf5_structure(self, group, indent=""):
        """递归打印HDF5结构"""
        for key in group.keys():
            item = group[key]
            if isinstance(item, h5py.Group):
                print(f"{indent}{key}/")
                self._print_hdf5_structure(item, indent + "  ")
            else:
                shape = item.shape if hasattr(item, 'shape') else 'scalar'
                dtype = item.dtype if hasattr(item, 'dtype') else 'unknown'
                print(f"{indent}{key}: {shape} ({dtype})")
    
    def check_data_consistency(self):
        """检查数据一致性"""
        print("\n🔄 检查数据一致性...")
        
        hdf5_files = list(self.data_path.glob("episode*.hdf5"))
        if not hdf5_files:
            return
        
        # 收集所有文件的统计信息
        episode_lengths = []
        image_shapes = defaultdict(list)
        action_shapes = []
        
        for hdf5_file in hdf5_files[:min(10, len(hdf5_files))]:  # 检查前10个文件
            try:
                with h5py.File(hdf5_file, 'r') as f:
                    # 检查episode长度
                    if 'joint_action' in f and 'left_arm' in f['joint_action']:
                        episode_length = len(f['joint_action/left_arm'])
                        episode_lengths.append(episode_length)
                    
                    # 检查图像尺寸
                    if 'observation' in f:
                        for cam_name in f['observation'].keys():
                            if 'rgb' in f[f'observation/{cam_name}']:
                                rgb_data = f[f'observation/{cam_name}/rgb']
                                if len(rgb_data) > 0:
                                    # 解码第一张图像检查尺寸
                                    try:
                                        img_bytes = rgb_data[0]
                                        img_array = np.frombuffer(img_bytes, dtype=np.uint8)
                                        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                                        if img is not None:
                                            image_shapes[cam_name].append(img.shape)
                                    except:
                                        pass
                    
                    # 检查动作维度
                    if 'joint_action' in f:
                        for arm in ['left_arm', 'right_arm']:
                            if arm in f['joint_action']:
                                action_shape = f[f'joint_action/{arm}'].shape
                                action_shapes.append(action_shape)
                                
            except Exception as e:
                self.issues.append(f"❌ 读取文件 {hdf5_file.name} 时出错: {str(e)}")
        
        # 分析统计结果
        if episode_lengths:
            avg_length = np.mean(episode_lengths)
            std_length = np.std(episode_lengths)
            print(f"📏 Episode长度: 平均 {avg_length:.1f} ± {std_length:.1f}")
            print(f"   范围: {min(episode_lengths)} - {max(episode_lengths)}")
            
            if std_length > avg_length * 0.3:  # 如果标准差超过平均值的30%
                self.issues.append("⚠️  Episode长度变化较大，可能影响训练")
        
        # 检查图像尺寸一致性
        for cam_name, shapes in image_shapes.items():
            unique_shapes = set(shapes)
            if len(unique_shapes) > 1:
                self.issues.append(f"❌ 相机 {cam_name} 图像尺寸不一致: {unique_shapes}")
            else:
                print(f"✅ 相机 {cam_name} 图像尺寸一致: {list(unique_shapes)[0]}")
    
    def check_data_quality(self):
        """检查数据质量"""
        print("\n⭐ 检查数据质量...")
        
        hdf5_files = list(self.data_path.glob("episode*.hdf5"))
        if not hdf5_files:
            return
        
        # 随机采样几个文件进行质量检查
        sample_files = np.random.choice(hdf5_files, min(5, len(hdf5_files)), replace=False)
        
        for hdf5_file in sample_files:
            try:
                with h5py.File(hdf5_file, 'r') as f:
                    # 检查动作数据的合理性
                    if 'joint_action' in f:
                        for arm in ['left_arm', 'right_arm']:
                            if arm in f['joint_action']:
                                actions = f[f'joint_action/{arm}'][()]
                                
                                # 检查是否有异常值
                                if np.any(np.isnan(actions)):
                                    self.issues.append(f"❌ {hdf5_file.name} {arm} 包含NaN值")
                                
                                if np.any(np.isinf(actions)):
                                    self.issues.append(f"❌ {hdf5_file.name} {arm} 包含无穷值")
                                
                                # 检查动作范围
                                action_range = np.max(actions) - np.min(actions)
                                if action_range < 1e-6:
                                    self.issues.append(f"⚠️  {hdf5_file.name} {arm} 动作变化很小")
                    
                    # 检查图像数据
                    if 'observation' in f:
                        for cam_name in f['observation'].keys():
                            if 'rgb' in f[f'observation/{cam_name}']:
                                rgb_data = f[f'observation/{cam_name}/rgb']
                                if len(rgb_data) == 0:
                                    self.issues.append(f"❌ {hdf5_file.name} {cam_name} 无图像数据")
                                
            except Exception as e:
                self.issues.append(f"❌ 质量检查 {hdf5_file.name} 时出错: {str(e)}")
    
    def generate_statistics(self):
        """生成统计报告"""
        print("\n📈 生成统计报告...")
        
        hdf5_files = list(self.data_path.glob("episode*.hdf5"))
        if not hdf5_files:
            return
        
        stats = {
            'total_episodes': len(hdf5_files),
            'total_size_mb': sum(f.stat().st_size for f in hdf5_files) / (1024 * 1024),
            'avg_file_size_mb': 0,
            'cameras': set(),
            'episode_lengths': []
        }
        
        # 收集详细统计
        for hdf5_file in hdf5_files[:min(20, len(hdf5_files))]:  # 采样检查
            try:
                with h5py.File(hdf5_file, 'r') as f:
                    # 收集相机信息
                    if 'observation' in f:
                        stats['cameras'].update(f['observation'].keys())
                    
                    # 收集episode长度
                    if 'joint_action' in f and 'left_arm' in f['joint_action']:
                        stats['episode_lengths'].append(len(f['joint_action/left_arm']))
                        
            except Exception:
                pass
        
        stats['avg_file_size_mb'] = stats['total_size_mb'] / stats['total_episodes']
        stats['cameras'] = list(stats['cameras'])
        
        # 打印统计信息
        print(f"📊 数据集统计:")
        print(f"   总episodes: {stats['total_episodes']}")
        print(f"   总大小: {stats['total_size_mb']:.1f} MB")
        print(f"   平均文件大小: {stats['avg_file_size_mb']:.1f} MB")
        print(f"   相机数量: {len(stats['cameras'])}")
        print(f"   相机列表: {stats['cameras']}")
        
        if stats['episode_lengths']:
            print(f"   平均episode长度: {np.mean(stats['episode_lengths']):.1f}")
            print(f"   Episode长度范围: {min(stats['episode_lengths'])} - {max(stats['episode_lengths'])}")
        
        return stats
    
    def save_report(self, output_file):
        """保存检查报告"""
        report = {
            'task_name': self.task_name,
            'data_path': str(self.data_path),
            'timestamp': str(np.datetime64('now')),
            'issues': self.issues,
            'statistics': self.generate_statistics()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 报告已保存到: {output_file}")
    
    def run_full_check(self):
        """运行完整检查"""
        print(f"🔍 开始检查数据质量: {self.data_path}")
        print("=" * 50)
        
        # 运行所有检查
        self.check_file_integrity()
        self.analyze_data_structure()
        self.check_data_consistency()
        self.check_data_quality()
        
        # 生成最终报告
        print("\n" + "=" * 50)
        print("📋 检查结果总结:")
        
        if not self.issues:
            print("🎉 恭喜！数据质量良好，未发现问题。")
        else:
            print(f"⚠️  发现 {len(self.issues)} 个问题:")
            for issue in self.issues:
                print(f"   {issue}")
        
        return len(self.issues) == 0

def main():
    parser = argparse.ArgumentParser(description='RoboTwin数据质量检查工具')
    parser.add_argument('data_path', help='数据目录路径')
    parser.add_argument('--task-name', default='unknown', help='任务名称')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 检查数据路径
    data_path = Path(args.data_path)
    if not data_path.exists():
        print(f"❌ 数据路径不存在: {data_path}")
        sys.exit(1)
    
    # 创建检查器
    checker = DataQualityChecker(data_path, args.task_name)
    
    # 运行检查
    success = checker.run_full_check()
    
    # 保存报告
    if args.output:
        checker.save_report(args.output)
    
    # 返回适当的退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
