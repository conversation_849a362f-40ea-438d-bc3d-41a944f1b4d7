# RoboTwin 故障排除指南

## 常见错误及解决方案

### 1. 配置文件错误

#### 错误: `YAML file is empty or invalid`
```bash
# 问题: YAML配置文件为空或格式错误
# 解决方案:
bash .update_path.sh  # 重新生成配置文件

# 检查配置文件
cat assets/embodiments/aloha-agilex-1/curobo_left.yml
```

#### 错误: `envsubst: command not found`
```bash
# 问题: 缺少envsubst工具
# 解决方案:
sudo apt-get update
sudo apt-get install -y gettext-base
```

### 2. 模型文件错误

#### 错误: `No available model_data.json files found`
```bash
# 问题: 缺少物体模型文件
# 解决方案1: 下载完整资源
cd assets
python _download.py

# 解决方案2: 创建基本模型文件（临时解决）
mkdir -p assets/objects/050_bell
echo '{"scale": [0.001, 0.001, 0.001], "transform_matrix": [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]], "contact_points_pose": [[0, 0, 0.05, 1, 0, 0, 0]], "functional_points_pose": [[0, 0, 0, 1, 0, 0, 0]]}' > assets/objects/050_bell/model_data0.json
```

#### 错误: `FileNotFoundError: model.urdf`
```bash
# 问题: 缺少URDF文件
# 解决方案: 创建基本URDF文件
mkdir -p assets/messy_objects/pencil/001
cat > assets/messy_objects/pencil/001/model.urdf << 'EOF'
<?xml version='1.0' encoding='utf-8'?>
<robot name="pencil">
  <link name="baseLink">
    <inertial>
       <mass value="0.01" />
       <inertia ixx="1e-06" ixy="0" ixz="0" iyy="1e-05" iyz="0" izz="1e-05" />
    </inertial>
    <visual>
      <geometry>
        <cylinder radius="0.003" length="0.15"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <cylinder radius="0.003" length="0.15"/>
      </geometry>
    </collision>
  </link>
</robot>
EOF
```

### 3. 环境问题

#### 错误: `conda: command not found`
```bash
# 问题: conda未安装或未正确配置
# 解决方案:
# 1. 安装miniconda
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh

# 2. 重新加载shell配置
source ~/.bashrc

# 3. 初始化conda
conda init bash
```

#### 错误: `Environment not found: RoboTwin_Challenge`
```bash
# 问题: conda环境不存在
# 解决方案:
conda env create -f environment.yml
conda activate RoboTwin_Challenge
```

### 4. GPU和渲染问题

#### 错误: `CUDA out of memory`
```bash
# 问题: GPU内存不足
# 解决方案:
# 1. 清理GPU内存
nvidia-smi --gpu-reset

# 2. 减少批处理大小或模型复杂度
# 3. 使用更小的场景配置
```

#### 错误: `Failed to find Vulkan ICD file`
```bash
# 问题: Vulkan驱动问题（警告，通常不影响运行）
# 解决方案:
# 1. 更新NVIDIA驱动
sudo apt-get update
sudo apt-get install nvidia-driver-470

# 2. 安装Vulkan支持
sudo apt-get install vulkan-utils
```

#### 错误: `Cannot connect to X server`
```bash
# 问题: 显示服务器问题
# 解决方案:
# 1. 启动虚拟显示
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &

# 2. 检查显示服务器状态
ps aux | grep Xvfb
```

### 5. 依赖包问题

#### 错误: `ModuleNotFoundError`
```bash
# 问题: Python包缺失
# 解决方案:
conda activate RoboTwin_Challenge
pip install -r requirements.txt

# 特定包安装示例:
pip install sapien
pip install mplib
pip install curobo
```

#### 错误: `ImportError: libGL.so.1`
```bash
# 问题: OpenGL库缺失
# 解决方案:
sudo apt-get install libgl1-mesa-glx libglib2.0-0
```

### 6. 网络和下载问题

#### 错误: `Connection timeout` (下载资源时)
```bash
# 问题: 网络连接问题
# 解决方案:
# 1. 使用代理
export https_proxy=http://proxy:port
export http_proxy=http://proxy:port

# 2. 手动下载资源
# 从备用源下载模型文件

# 3. 使用镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 7. 权限问题

#### 错误: `Permission denied`
```bash
# 问题: 文件权限不足
# 解决方案:
chmod +x run_task_headless.sh
chmod +x .update_path.sh

# 如果需要sudo权限
sudo chown -R $USER:$USER /path/to/RoboTwin
```

### 8. 内存问题

#### 错误: `Killed` (进程被杀死)
```bash
# 问题: 内存不足
# 解决方案:
# 1. 检查内存使用
free -h
top

# 2. 增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 3. 清理内存
sudo sync && sudo sysctl vm.drop_caches=3
```

## 调试技巧

### 1. 详细日志
```bash
# 启用详细输出
export PYTHONPATH=/root/autodl-tmp/RoboTwin:$PYTHONPATH
export CUDA_LAUNCH_BLOCKING=1

# 运行时保存日志
bash run_task_headless.sh place_object_scale 0 2>&1 | tee debug.log
```

### 2. 分步调试
```bash
# 1. 测试环境
python -c "import sapien; print('SAPIEN OK')"
python -c "import torch; print('PyTorch OK')"

# 2. 测试配置
python -c "
import yaml
with open('assets/embodiments/aloha-agilex-1/curobo_left.yml', 'r') as f:
    data = yaml.safe_load(f)
print('Config OK:', data is not None)
"

# 3. 测试模型加载
python -c "
import os
print('Objects dir:', os.listdir('assets/objects/'))
"
```

### 3. 性能监控
```bash
# GPU监控
watch -n 1 nvidia-smi

# 系统监控
htop

# 磁盘空间
df -h
```

## 预防措施

1. **定期备份**: 备份重要的配置和模型文件
2. **环境隔离**: 使用conda环境避免包冲突
3. **资源监控**: 定期检查GPU、内存和磁盘使用情况
4. **版本控制**: 记录工作的软件版本
5. **文档记录**: 记录自定义修改和配置

## 获取帮助

如果以上解决方案都无法解决问题：

1. 检查GitHub Issues
2. 查看官方文档
3. 联系技术支持
4. 在社区论坛求助

记住：大多数问题都有解决方案，保持耐心和系统性的调试方法。
