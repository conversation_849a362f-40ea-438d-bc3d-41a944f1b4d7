# RoboTwin 数据生成与模型训练教程

## 📋 概述

RoboTwin提供了完整的数据生成和模型训练流程，支持多种策略学习方法包括ACT和RDT。本教程将详细介绍如何生成训练数据并训练机器人策略模型。

## 🎯 数据生成流程

### 1. 数据生成原理

RoboTwin的数据生成分为两个阶段：
1. **种子搜索阶段**: 寻找能够成功完成任务的随机种子
2. **数据收集阶段**: 使用成功的种子重放并收集多模态数据

### 2. 数据类型

生成的数据包括：
- **RGB图像**: 头部相机和腕部相机的彩色图像
- **深度图像**: 深度信息（可选）
- **关节状态**: 机器人关节位置和速度
- **末端执行器位置**: 机械臂末端位姿
- **动作序列**: 机器人执行的动作轨迹

## 🚀 快速开始

### 1. 基本数据生成命令

```bash
# 基本语法
bash run_task.sh <任务名称> <GPU编号>

# 无头模式（推荐用于服务器）
bash run_task_headless.sh <任务名称> <GPU编号>

# 示例：生成放置物体到秤上的任务数据
bash run_task_headless.sh place_object_scale 0
```

### 2. 支持的任务

```bash
# 主要任务列表
bash run_task_headless.sh place_object_scale 0    # 放置物体到电子秤
bash run_task_headless.sh blocks_stack_three 0    # 堆叠三个方块
bash run_task_headless.sh blocks_ranking_rgb 0    # 按颜色排列方块
bash run_task_headless.sh place_phone_stand 0     # 放置手机支架
bash run_task_headless.sh dual_shoes_place 0      # 双臂放置鞋子
```

## ⚙️ 配置文件详解

### 1. 任务配置文件

每个任务的配置文件位于 `task_config/<任务名称>.yml`：

```yaml
# 示例：task_config/place_object_scale.yml
task_name: "place_object_scale"
render_freq: 0                    # 渲染频率（0=不渲染）
episode_num: 100                  # 生成的数据集数量
use_seed: false                   # 是否使用预定义种子
save_freq: 10                     # 保存频率
embodiment: ["aloha-agilex-1"]    # 机器人配置

# 数据增强配置
augmentation:
  random_background: true         # 随机背景
  messy_table: true              # 杂乱桌面
  clean_background_rate: 0.1     # 干净背景比例
  random_head_camera_dis: 0      # 头部相机随机距离
  random_table_height: 0.03      # 桌面高度随机化
  random_light: true             # 随机光照
  crazy_random_light_rate: 0.1   # 极端光照比例

# 相机配置
camera:
  head_camera_type: "D435"       # 头部相机类型
  wrist_camera_type: "D435"      # 腕部相机类型
  collect_head_camera: true      # 收集头部相机数据
  collect_wrist_camera: true     # 收集腕部相机数据

# 数据类型配置
data_type:
  rgb: true                      # RGB图像
  depth: false                   # 深度图像
  observer: false                # 观察者视角
  endpose: false                 # 末端位姿
  qpos: true                     # 关节位置

# 其他配置
pcd_down_sample_num: 1024        # 点云下采样数量
dual_arm: true                   # 双臂模式
pcd_crop: true                   # 点云裁剪
save_path: "./data"              # 数据保存路径
st_episode: 0                    # 起始集数
collect_data: true               # 是否收集数据
eval_video_log: true             # 是否生成评估视频
```

### 2. 自定义配置

```bash
# 修改配置文件
cp task_config/place_object_scale.yml task_config/my_custom_task.yml

# 编辑配置
vim task_config/my_custom_task.yml
```

## 📊 数据生成过程详解

### 1. 种子搜索阶段

```bash
# 系统会自动搜索成功的种子
[Start Seed and Pre Motion Data Collection]
simulate data episode 0 fail! (seed = 0)
simulate data episode 0 fail! (seed = 1)
...
simulate data episode 0 success! (seed = 11)
simulate data episode 1 success! (seed = 15)
```

### 2. 数据收集阶段

```bash
# 使用成功的种子重放并收集数据
[Start Data Collection]
episode 0 success! (seed = 11)
episode 1 success! (seed = 15)
...
episode 99 success! (seed = 234)
```

### 3. 数据存储结构

```
data/
├── <任务名称>/
│   ├── <设置>/
│   │   ├── episode0.hdf5          # 第0集数据
│   │   ├── episode1.hdf5          # 第1集数据
│   │   ├── ...
│   │   ├── episode99.hdf5         # 第99集数据
│   │   ├── scene_info.json        # 场景信息
│   │   └── videos/                # 视频文件
│   │       ├── episode0.mp4
│   │       ├── episode1.mp4
│   │       └── ...
```

## 🔄 数据格式转换

### 1. ACT格式转换

```bash
# 转换为ACT训练格式
bash process_data_act.sh <任务名称> <相机类型> <数据集数量> <机器人配置> <GPU编号>

# 示例
bash process_data_act.sh place_object_scale D435 50 aloha-agilex-1 0
```

转换后的数据结构：
```
policy/ACT/data/
├── sim_place_object_scale_D435_50/
│   ├── episode_0.hdf5
│   ├── episode_1.hdf5
│   └── ...
```

### 2. RDT格式转换

```bash
# 转换为RDT训练格式
bash process_data_rdt.sh <任务名称> <设置> <数据集数量> <GPU编号>

# 示例
bash process_data_rdt.sh place_object_scale default 50 0
```

## 🎓 模型训练

### 1. ACT模型训练

#### 步骤1: 添加任务配置
编辑 `policy/ACT/constants.py`：

```python
SIM_TASK_CONFIGS = {
    # 添加新任务配置
    'sim_place_object_scale_D435_50': {
        'dataset_dir': DATA_DIR + '/sim_place_object_scale_D435_50',
        'num_episodes': 50,
        'episode_len': 500,
        'camera_names': ["cam_high", "cam_right_wrist", "cam_left_wrist"]
    }
}
```

#### 步骤2: 开始训练
```bash
cd policy/ACT
python train.py \
    --task_name sim_place_object_scale_D435_50 \
    --ckpt_dir ./ckpt \
    --policy_class ACT \
    --kl_weight 10 \
    --chunk_size 100 \
    --hidden_dim 512 \
    --batch_size 8 \
    --dim_feedforward 3200 \
    --num_epochs 2000 \
    --lr 1e-5 \
    --seed 0
```

### 2. RDT模型训练

#### 步骤1: 生成指令数据
```bash
cd policy/RDT
bash generate.sh
```

#### 步骤2: 微调训练
```bash
bash finetune.sh
```

## 📈 监控和调试

### 1. 数据生成监控

```bash
# 实时查看生成进度
tail -f nohup.out

# 检查GPU使用情况
watch -n 1 nvidia-smi

# 检查数据文件
ls -la data/<任务名称>/<设置>/
```

### 2. 训练监控

```bash
# 查看训练日志
tensorboard --logdir=./logs

# 监控训练进度
tail -f train.log
```

## 🔧 高级配置

### 1. 批量数据生成

```bash
#!/bin/bash
# 批量生成多个任务的数据

tasks=("place_object_scale" "blocks_stack_three" "blocks_ranking_rgb")

for task in "${tasks[@]}"; do
    echo "生成任务: $task"
    bash run_task_headless.sh $task 0
    echo "任务 $task 完成"
done
```

### 2. 分布式数据生成

```bash
# 在多个GPU上并行生成数据
bash run_task_headless.sh place_object_scale 0 &
bash run_task_headless.sh blocks_stack_three 1 &
bash run_task_headless.sh blocks_ranking_rgb 2 &
wait
```

### 3. 自定义数据增强

修改任务配置文件中的增强参数：

```yaml
augmentation:
  random_background: true
  messy_table: true
  clean_background_rate: 0.2      # 增加干净背景比例
  random_head_camera_dis: 0.1     # 增加相机位置随机性
  random_table_height: 0.05       # 增加桌面高度变化
  random_light: true
  crazy_random_light_rate: 0.2    # 增加极端光照比例
```

## 💡 最佳实践

### 1. 数据质量保证
- 确保足够的数据多样性
- 检查任务成功率（建议>80%）
- 验证数据完整性

### 2. 训练优化
- 使用适当的批次大小
- 调整学习率和训练轮数
- 定期保存检查点

### 3. 资源管理
- 监控GPU内存使用
- 合理分配存储空间
- 定期清理临时文件

## 🚨 常见问题

### 1. 数据生成失败
```bash
# 检查模型文件
ls -la assets/objects/

# 检查配置文件
cat assets/embodiments/aloha-agilex-1/curobo_left.yml

# 重新生成配置
bash .update_path.sh
```

### 2. 内存不足
```bash
# 减少批次大小
# 降低图像分辨率
# 使用数据流式加载
```

### 3. 训练收敛慢
```bash
# 增加数据量
# 调整学习率
# 使用预训练模型
```

## 📚 相关文档

- [无头模式脚本使用教程](无头模式脚本使用教程.md)
- [故障排除指南](故障排除指南.md)
- [ACT训练文档](../policy/ACT/README.md)
- [RDT训练文档](../policy/RDT/README.md)

通过本教程，您应该能够成功生成高质量的训练数据并训练出有效的机器人策略模型。
