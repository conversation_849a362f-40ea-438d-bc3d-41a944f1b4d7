# RoboTwin 无头模式脚本使用教程

## 概述

无头模式（Headless Mode）脚本允许您在没有图形界面的服务器环境中运行RoboTwin任务。这对于AutoDL、云服务器或远程计算环境特别有用。

## 脚本文件

主要的无头模式脚本是 `run_task_headless.sh`，位于RoboTwin项目根目录。

## 基本用法

### 语法
```bash
bash run_task_headless.sh <任务名称> <GPU编号>
```

### 参数说明
- `<任务名称>`: 要运行的任务名称（如 place_object_scale）
- `<GPU编号>`: 使用的GPU设备编号（通常为0）

### 示例
```bash
# 运行放置物体到秤上的任务，使用GPU 0
bash run_task_headless.sh place_object_scale 0

# 运行其他任务示例
bash run_task_headless.sh pick_and_place 0
bash run_task_headless.sh stack_objects 0
```

## 脚本功能

### 1. 环境检查
脚本会自动检查：
- 参数是否正确
- GPU是否可用
- 必要的依赖是否安装

### 2. 虚拟显示设置
```bash
# 自动启动Xvfb虚拟显示服务器
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
```

### 3. 环境激活
```bash
# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate RoboTwin_Challenge
```

### 4. 路径更新
```bash
# 更新配置文件中的路径
bash .update_path.sh
```

## 输出信息

### 启动信息
```
🤖 启动RoboTwin任务: place_object_scale (GPU: 0)
🖥️  启动虚拟显示服务器...
✅ 虚拟显示服务器已运行
🚀 运行任务: place_object_scale
📊 GPU状态:
0, NVIDIA GeForce RTX 3080 Ti, 2, 12288
```

### 配置信息
```
============= Config =============
Messy Table: True
Random Background: True
 - Clean Background Rate: 0.1
Random Light: True
 - Crazy Random Light Rate: 0.1
Random Table Height: 0.03
Random Head Camera Distance: 0
Head Camera Config: D435, True
Wrist Camera Config: D435, True
Embodiment Config: aloha-agilex-1
==================================
```

### 任务执行信息
```
Task name: place_object_scale
[Start Seed and Pre Motion Data Collection]
simulate data episode 0 success! (seed = 11)
simulate data episode 1 success! (seed = 15)
...
```

## 常见问题与解决方案

### 1. 权限问题
```bash
# 如果遇到权限错误，添加执行权限
chmod +x run_task_headless.sh
```

### 2. 显示问题
```bash
# 检查Xvfb是否安装
sudo apt-get install xvfb

# 检查虚拟显示是否运行
ps aux | grep Xvfb
```

### 3. 环境问题
```bash
# 确保conda环境存在
conda env list

# 重新创建环境（如果需要）
conda env create -f environment.yml
```

### 4. GPU问题
```bash
# 检查GPU状态
nvidia-smi

# 检查CUDA是否可用
python -c "import torch; print(torch.cuda.is_available())"
```

## 高级用法

### 1. 批量运行任务
```bash
#!/bin/bash
# 批量运行脚本示例
tasks=("place_object_scale" "pick_and_place" "stack_objects")

for task in "${tasks[@]}"; do
    echo "运行任务: $task"
    bash run_task_headless.sh $task 0
    echo "任务 $task 完成"
done
```

### 2. 日志记录
```bash
# 将输出保存到日志文件
bash run_task_headless.sh place_object_scale 0 > task_log.txt 2>&1

# 实时查看日志
bash run_task_headless.sh place_object_scale 0 | tee task_log.txt
```

### 3. 后台运行
```bash
# 在后台运行任务
nohup bash run_task_headless.sh place_object_scale 0 > task_log.txt 2>&1 &

# 查看后台任务
jobs

# 查看进程
ps aux | grep python
```

## 性能优化

### 1. 内存管理
```bash
# 监控内存使用
watch -n 1 'free -h'

# 清理内存（如果需要）
sudo sync && sudo sysctl vm.drop_caches=3
```

### 2. GPU监控
```bash
# 实时监控GPU使用
watch -n 1 nvidia-smi

# 监控GPU内存
nvidia-smi --query-gpu=memory.used,memory.total --format=csv
```

## 故障排除

### 1. 任务失败
如果任务失败，检查：
- 模型文件是否完整
- 配置文件是否正确
- 依赖包是否安装

### 2. 渲染问题
```bash
# 检查渲染后端
echo $DISPLAY

# 重启虚拟显示
pkill Xvfb
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
export DISPLAY=:99
```

### 3. 路径问题
```bash
# 手动更新路径
bash .update_path.sh

# 检查配置文件
cat assets/embodiments/aloha-agilex-1/curobo_left.yml
```

## 最佳实践

1. **运行前检查**: 确保所有依赖和资源文件都已准备就绪
2. **监控资源**: 定期检查GPU和内存使用情况
3. **日志管理**: 保存运行日志以便调试
4. **定期清理**: 清理临时文件和缓存
5. **备份配置**: 备份重要的配置文件

## 总结

无头模式脚本为在服务器环境中运行RoboTwin提供了便利的解决方案。通过正确配置和使用，您可以高效地进行机器人仿真和训练任务。

如有问题，请检查日志输出并参考故障排除部分。
