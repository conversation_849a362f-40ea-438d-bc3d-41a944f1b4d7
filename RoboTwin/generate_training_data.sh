#!/bin/bash

# RoboTwin 训练数据生成脚本
# 作者: Augment Agent
# 用途: 自动化训练数据生成和格式转换

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "🤖 RoboTwin 训练数据生成脚本"
    echo ""
    echo "用法:"
    echo "  $0 [选项] <任务名称> <GPU编号>"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -e, --episodes NUM      设置生成的数据集数量 (默认: 100)"
    echo "  -f, --format FORMAT     数据格式 (act|rdt|both) (默认: both)"
    echo "  -c, --camera TYPE       相机类型 (默认: D435)"
    echo "  -b, --embodiment NAME   机器人配置 (默认: aloha-agilex-1)"
    echo "  -o, --output DIR        输出目录 (默认: ./data)"
    echo "  -s, --setting NAME      设置名称 (默认: default)"
    echo "  --headless              使用无头模式"
    echo "  --no-video              不生成视频"
    echo "  --monitor               启用实时监控"
    echo ""
    echo "支持的任务:"
    echo "  place_object_scale      放置物体到电子秤"
    echo "  blocks_stack_three      堆叠三个方块"
    echo "  blocks_ranking_rgb      按颜色排列方块"
    echo "  place_phone_stand       放置手机支架"
    echo "  dual_shoes_place        双臂放置鞋子"
    echo ""
    echo "示例:"
    echo "  $0 place_object_scale 0                    # 基本用法"
    echo "  $0 -e 50 -f act place_object_scale 0       # 生成50集ACT格式数据"
    echo "  $0 --headless --monitor place_object_scale 0  # 无头模式+监控"
}

# 默认参数
EPISODES=100
FORMAT="both"
CAMERA_TYPE="D435"
EMBODIMENT="aloha-agilex-1"
OUTPUT_DIR="./data"
SETTING="default"
HEADLESS=false
NO_VIDEO=false
MONITOR=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -e|--episodes)
            EPISODES="$2"
            shift 2
            ;;
        -f|--format)
            FORMAT="$2"
            shift 2
            ;;
        -c|--camera)
            CAMERA_TYPE="$2"
            shift 2
            ;;
        -b|--embodiment)
            EMBODIMENT="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -s|--setting)
            SETTING="$2"
            shift 2
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        --no-video)
            NO_VIDEO=true
            shift
            ;;
        --monitor)
            MONITOR=true
            shift
            ;;
        -*)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [[ -z "$TASK_NAME" ]]; then
                TASK_NAME="$1"
            elif [[ -z "$GPU_ID" ]]; then
                GPU_ID="$1"
            else
                log_error "过多的参数: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [[ -z "$TASK_NAME" ]] || [[ -z "$GPU_ID" ]]; then
    log_error "缺少必需参数"
    show_help
    exit 1
fi

# 验证参数
if [[ ! "$FORMAT" =~ ^(act|rdt|both)$ ]]; then
    log_error "无效的格式: $FORMAT (支持: act, rdt, both)"
    exit 1
fi

if [[ ! "$EPISODES" =~ ^[0-9]+$ ]] || [[ "$EPISODES" -lt 1 ]]; then
    log_error "无效的数据集数量: $EPISODES"
    exit 1
fi

# 显示配置信息
show_config() {
    echo "🎯 数据生成配置"
    echo "=================================="
    echo "任务名称: $TASK_NAME"
    echo "GPU编号: $GPU_ID"
    echo "数据集数量: $EPISODES"
    echo "数据格式: $FORMAT"
    echo "相机类型: $CAMERA_TYPE"
    echo "机器人配置: $EMBODIMENT"
    echo "输出目录: $OUTPUT_DIR"
    echo "设置名称: $SETTING"
    echo "无头模式: $HEADLESS"
    echo "生成视频: $([ "$NO_VIDEO" = true ] && echo "否" || echo "是")"
    echo "实时监控: $MONITOR"
    echo "=================================="
    echo ""
}

# 检查环境
check_environment() {
    log_step "检查环境..."
    
    # 检查GPU
    if ! command -v nvidia-smi &> /dev/null; then
        log_warning "未检测到NVIDIA GPU"
    else
        log_info "GPU状态:"
        nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits | head -1
    fi
    
    # 检查conda环境
    if ! conda env list | grep -q "RoboTwin_Challenge"; then
        log_error "RoboTwin_Challenge环境不存在，请先运行 bash setup_headless.sh"
        exit 1
    fi
    
    # 检查任务配置文件
    if [[ ! -f "task_config/${TASK_NAME}.yml" ]]; then
        log_error "任务配置文件不存在: task_config/${TASK_NAME}.yml"
        exit 1
    fi
    
    log_success "环境检查完成"
}

# 启动监控
start_monitoring() {
    if [[ "$MONITOR" = true ]]; then
        log_info "启动实时监控..."
        
        # 在后台启动GPU监控
        watch -n 2 nvidia-smi > gpu_monitor.log 2>&1 &
        GPU_MONITOR_PID=$!
        
        # 在后台启动磁盘监控
        watch -n 5 "df -h | grep -E '(Filesystem|/dev/)'" > disk_monitor.log 2>&1 &
        DISK_MONITOR_PID=$!
        
        log_success "监控已启动 (GPU PID: $GPU_MONITOR_PID, Disk PID: $DISK_MONITOR_PID)"
    fi
}

# 停止监控
stop_monitoring() {
    if [[ "$MONITOR" = true ]]; then
        log_info "停止监控..."
        
        if [[ -n "$GPU_MONITOR_PID" ]]; then
            kill $GPU_MONITOR_PID 2>/dev/null || true
        fi
        
        if [[ -n "$DISK_MONITOR_PID" ]]; then
            kill $DISK_MONITOR_PID 2>/dev/null || true
        fi
        
        # 清理监控日志
        rm -f gpu_monitor.log disk_monitor.log
        
        log_success "监控已停止"
    fi
}

# 生成原始数据
generate_raw_data() {
    log_step "生成原始数据..."
    
    # 更新配置文件
    bash .update_path.sh > /dev/null 2>&1
    
    # 选择运行脚本
    if [[ "$HEADLESS" = true ]]; then
        RUN_SCRIPT="run_task_headless.sh"
    else
        RUN_SCRIPT="run_task.sh"
    fi
    
    # 生成数据
    log_info "开始生成 $EPISODES 集数据..."
    start_time=$(date +%s)
    
    if bash $RUN_SCRIPT $TASK_NAME $GPU_ID; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        log_success "原始数据生成完成 (耗时: ${duration}秒)"
    else
        log_error "原始数据生成失败"
        return 1
    fi
}

# 转换为ACT格式
convert_to_act() {
    log_step "转换为ACT格式..."
    
    if bash process_data_act.sh $TASK_NAME $CAMERA_TYPE $EPISODES $EMBODIMENT $GPU_ID; then
        log_success "ACT格式转换完成"
        
        # 显示输出路径
        ACT_OUTPUT="policy/ACT/data/sim_${TASK_NAME}_${CAMERA_TYPE}_${EPISODES}"
        log_info "ACT数据保存在: $ACT_OUTPUT"
        
        # 显示数据统计
        if [[ -d "$ACT_OUTPUT" ]]; then
            file_count=$(ls -1 "$ACT_OUTPUT"/*.hdf5 2>/dev/null | wc -l)
            total_size=$(du -sh "$ACT_OUTPUT" 2>/dev/null | cut -f1)
            log_info "生成文件数: $file_count, 总大小: $total_size"
        fi
    else
        log_error "ACT格式转换失败"
        return 1
    fi
}

# 转换为RDT格式
convert_to_rdt() {
    log_step "转换为RDT格式..."
    
    if bash process_data_rdt.sh $TASK_NAME $SETTING $EPISODES $GPU_ID; then
        log_success "RDT格式转换完成"
        
        # 显示输出路径
        RDT_OUTPUT="policy/RDT/data/${TASK_NAME}_${SETTING}_${EPISODES}"
        log_info "RDT数据保存在: $RDT_OUTPUT"
    else
        log_error "RDT格式转换失败"
        return 1
    fi
}

# 生成训练配置
generate_training_config() {
    log_step "生成训练配置..."
    
    if [[ "$FORMAT" = "act" ]] || [[ "$FORMAT" = "both" ]]; then
        # 生成ACT配置
        ACT_CONFIG_KEY="sim_${TASK_NAME}_${CAMERA_TYPE}_${EPISODES}"
        ACT_CONFIG_FILE="policy/ACT/configs/${TASK_NAME}_config.py"
        
        cat > "$ACT_CONFIG_FILE" << EOF
# ACT训练配置 - 自动生成
# 任务: $TASK_NAME
# 数据集: $EPISODES 集

TASK_CONFIG = {
    '$ACT_CONFIG_KEY': {
        'dataset_dir': './data/sim_${TASK_NAME}_${CAMERA_TYPE}_${EPISODES}',
        'num_episodes': $EPISODES,
        'episode_len': 500,
        'camera_names': ["cam_high", "cam_right_wrist", "cam_left_wrist"]
    }
}

# 训练参数
TRAIN_CONFIG = {
    'task_name': '$ACT_CONFIG_KEY',
    'ckpt_dir': './ckpt/${TASK_NAME}',
    'policy_class': 'ACT',
    'kl_weight': 10,
    'chunk_size': 100,
    'hidden_dim': 512,
    'batch_size': 8,
    'dim_feedforward': 3200,
    'num_epochs': 2000,
    'lr': 1e-5,
    'seed': 0
}
EOF
        
        log_info "ACT配置文件已生成: $ACT_CONFIG_FILE"
    fi
}

# 显示训练命令
show_training_commands() {
    log_step "训练命令"
    echo ""
    
    if [[ "$FORMAT" = "act" ]] || [[ "$FORMAT" = "both" ]]; then
        echo "🎓 ACT模型训练:"
        echo "cd policy/ACT"
        echo "python train.py \\"
        echo "    --task_name sim_${TASK_NAME}_${CAMERA_TYPE}_${EPISODES} \\"
        echo "    --ckpt_dir ./ckpt/${TASK_NAME} \\"
        echo "    --policy_class ACT \\"
        echo "    --kl_weight 10 \\"
        echo "    --chunk_size 100 \\"
        echo "    --hidden_dim 512 \\"
        echo "    --batch_size 8 \\"
        echo "    --dim_feedforward 3200 \\"
        echo "    --num_epochs 2000 \\"
        echo "    --lr 1e-5 \\"
        echo "    --seed 0"
        echo ""
    fi
    
    if [[ "$FORMAT" = "rdt" ]] || [[ "$FORMAT" = "both" ]]; then
        echo "🎓 RDT模型训练:"
        echo "cd policy/RDT"
        echo "bash generate.sh"
        echo "bash finetune.sh"
        echo ""
    fi
}

# 清理函数
cleanup() {
    log_info "清理资源..."
    stop_monitoring
    
    # 清理临时文件
    rm -f nohup.out
    
    log_success "清理完成"
}

# 主函数
main() {
    # 设置清理陷阱
    trap cleanup EXIT INT TERM
    
    echo "🤖 RoboTwin 训练数据生成脚本"
    echo ""
    
    show_config
    check_environment
    start_monitoring
    
    # 记录开始时间
    TOTAL_START_TIME=$(date +%s)
    
    # 生成原始数据
    if ! generate_raw_data; then
        log_error "数据生成失败"
        exit 1
    fi
    
    # 格式转换
    if [[ "$FORMAT" = "act" ]] || [[ "$FORMAT" = "both" ]]; then
        if ! convert_to_act; then
            log_error "ACT格式转换失败"
            exit 1
        fi
    fi
    
    if [[ "$FORMAT" = "rdt" ]] || [[ "$FORMAT" = "both" ]]; then
        if ! convert_to_rdt; then
            log_error "RDT格式转换失败"
            exit 1
        fi
    fi
    
    # 生成训练配置
    generate_training_config
    
    # 计算总耗时
    TOTAL_END_TIME=$(date +%s)
    TOTAL_DURATION=$((TOTAL_END_TIME - TOTAL_START_TIME))
    
    echo ""
    log_success "🎉 数据生成完成!"
    log_info "总耗时: ${TOTAL_DURATION}秒"
    echo ""
    
    show_training_commands
}

# 运行主函数
main "$@"
