#!/bin/bash

# RoboTwin 无头模式运行脚本
# 专为AutoDL环境设计

task_name=${1}
gpu_id=${2:-0}

echo "🤖 启动RoboTwin任务: $task_name (GPU: $gpu_id)"

# 检查参数
if [ -z "$task_name" ]; then
    echo "❌ 错误: 请提供任务名称"
    echo "用法: bash run_task_headless.sh <task_name> [gpu_id]"
    echo "可用任务:"
    echo "  - place_object_scale"
    echo "  - blocks_ranking_rgb"
    echo "  - blocks_stack_three"
    echo "  - dual_shoes_place"
    echo "  - place_phone_stand"
    echo "  - put_bottles_dustbin"
    exit 1
fi

# 设置环境变量
export CUDA_VISIBLE_DEVICES=${gpu_id}
export DISPLAY=:99
export PYTHONPATH="${PWD}:${PYTHONPATH}"

# 启动虚拟显示服务器
echo "🖥️  启动虚拟显示服务器..."
if ! pgrep -x "Xvfb" > /dev/null; then
    Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
    sleep 3
    echo "✅ 虚拟显示服务器已启动"
else
    echo "✅ 虚拟显示服务器已运行"
fi

# 更新路径
./.update_path.sh > /dev/null 2>&1

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate RoboTwin_Challenge

# 运行任务
echo "🚀 运行任务: $task_name"
echo "📊 GPU状态:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits | head -1

echo ""
echo "开始执行..."
echo ${task_name} | python script/run_task.py

echo ""
echo "✅ 任务完成!"
