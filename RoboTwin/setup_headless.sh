#!/bin/bash

# RoboTwin 无头模式快速设置脚本
# 作者: Augment Agent
# 用途: 一键设置RoboTwin无头模式运行环境

set -e  # 遇到错误立即退出

echo "🤖 RoboTwin 无头模式环境设置脚本"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户运行"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持Linux系统"
        exit 1
    fi
    
    # 检查GPU
    if ! command -v nvidia-smi &> /dev/null; then
        log_warning "未检测到NVIDIA GPU或驱动"
    else
        log_success "检测到NVIDIA GPU"
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
    fi
    
    # 检查内存
    total_mem=$(free -g | awk '/^Mem:/{print $2}')
    if [[ $total_mem -lt 8 ]]; then
        log_warning "系统内存少于8GB，可能影响性能"
    else
        log_success "系统内存: ${total_mem}GB"
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    # 更新包列表
    sudo apt-get update -qq
    
    # 安装必要的包
    sudo apt-get install -y \
        wget \
        curl \
        git \
        build-essential \
        xvfb \
        libgl1-mesa-glx \
        libglib2.0-0 \
        gettext-base \
        htop \
        tree
    
    log_success "系统依赖安装完成"
}

# 安装/检查conda
setup_conda() {
    log_info "设置Conda环境..."
    
    if ! command -v conda &> /dev/null; then
        log_info "Conda未安装，正在下载安装..."
        
        # 下载miniconda
        wget -q https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh
        
        # 安装miniconda
        bash miniconda.sh -b -p $HOME/miniconda3
        
        # 初始化conda
        $HOME/miniconda3/bin/conda init bash
        
        # 重新加载bashrc
        source ~/.bashrc
        
        # 清理安装文件
        rm miniconda.sh
        
        log_success "Conda安装完成"
    else
        log_success "Conda已安装"
    fi
}

# 创建conda环境
create_conda_env() {
    log_info "创建RoboTwin conda环境..."
    
    # 激活conda
    source ~/miniconda3/etc/profile.d/conda.sh
    
    # 检查环境是否存在
    if conda env list | grep -q "RoboTwin_Challenge"; then
        log_warning "RoboTwin_Challenge环境已存在"
        read -p "是否重新创建? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            conda env remove -n RoboTwin_Challenge -y
        else
            log_info "跳过环境创建"
            return
        fi
    fi
    
    # 创建环境
    if [[ -f "environment.yml" ]]; then
        conda env create -f environment.yml
    else
        log_warning "未找到environment.yml，创建基础Python环境"
        conda create -n RoboTwin_Challenge python=3.10 -y
    fi
    
    log_success "Conda环境创建完成"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    
    # 激活环境
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate RoboTwin_Challenge
    
    # 安装基础依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
    else
        log_warning "未找到requirements.txt，安装基础包"
        pip install torch torchvision numpy scipy matplotlib pyyaml
    fi
    
    log_success "Python依赖安装完成"
}

# 设置虚拟显示
setup_virtual_display() {
    log_info "设置虚拟显示..."
    
    # 检查Xvfb是否运行
    if pgrep -x "Xvfb" > /dev/null; then
        log_warning "Xvfb已在运行"
    else
        # 启动虚拟显示
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
        export DISPLAY=:99
        
        # 添加到bashrc
        if ! grep -q "export DISPLAY=:99" ~/.bashrc; then
            echo "export DISPLAY=:99" >> ~/.bashrc
        fi
        
        log_success "虚拟显示设置完成"
    fi
}

# 更新配置文件
update_configs() {
    log_info "更新配置文件..."
    
    # 运行路径更新脚本
    if [[ -f ".update_path.sh" ]]; then
        bash .update_path.sh
        log_success "配置文件更新完成"
    else
        log_warning "未找到.update_path.sh脚本"
    fi
}

# 创建基础模型文件（如果需要）
create_basic_models() {
    log_info "检查模型文件..."
    
    # 检查objects目录
    if [[ ! -d "assets/objects" ]] || [[ -z "$(ls -A assets/objects 2>/dev/null)" ]]; then
        log_warning "objects目录为空，创建基础模型文件"
        
        # 创建必要的目录和文件
        mkdir -p assets/objects/{047_mouse,048_stapler,050_bell,072_electronicscale}
        
        # 创建基础模型数据
        for obj in 047_mouse 048_stapler 050_bell; do
            echo '{"scale": [0.001, 0.001, 0.001], "transform_matrix": [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]], "contact_points_pose": [[0, 0, 0.05, 1, 0, 0, 0]], "functional_points_pose": [[0, 0, 0, 1, 0, 0, 0]]}' > assets/objects/${obj}/model_data0.json
        done
        
        # 为电子秤创建多个模型
        for id in 0 1 5 6; do
            echo '{"scale": [0.001, 0.001, 0.001], "transform_matrix": [[1, 0, 0, 0], [0, 1, 0, 0], [0, 0, 1, 0], [0, 0, 0, 1]], "contact_points_pose": [[0, 0, 0.05, 1, 0, 0, 0]], "functional_points_pose": [[0, 0, 0, 1, 0, 0, 0]]}' > assets/objects/072_electronicscale/model_data${id}.json
        done
        
        log_success "基础模型文件创建完成"
    else
        log_success "模型文件已存在"
    fi
}

# 测试安装
test_installation() {
    log_info "测试安装..."
    
    # 激活环境
    source ~/miniconda3/etc/profile.d/conda.sh
    conda activate RoboTwin_Challenge
    
    # 测试Python导入
    python -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import torch
    print(f'PyTorch版本: {torch.__version__}')
    print(f'CUDA可用: {torch.cuda.is_available()}')
except ImportError:
    print('PyTorch未安装')

try:
    import numpy
    print(f'NumPy版本: {numpy.__version__}')
except ImportError:
    print('NumPy未安装')
"
    
    # 测试配置文件
    if [[ -f "assets/embodiments/aloha-agilex-1/curobo_left.yml" ]]; then
        log_success "配置文件存在"
    else
        log_warning "配置文件不存在"
    fi
    
    log_success "安装测试完成"
}

# 显示使用说明
show_usage() {
    echo ""
    echo "🎉 设置完成！"
    echo ""
    echo "使用方法:"
    echo "  bash run_task_headless.sh <任务名称> <GPU编号>"
    echo ""
    echo "示例:"
    echo "  bash run_task_headless.sh place_object_scale 0"
    echo ""
    echo "更多信息请查看:"
    echo "  - docs/无头模式脚本使用教程.md"
    echo "  - docs/故障排除指南.md"
    echo ""
}

# 主函数
main() {
    echo "开始设置RoboTwin无头模式环境..."
    echo ""
    
    check_root
    check_system
    install_system_deps
    setup_conda
    create_conda_env
    install_python_deps
    setup_virtual_display
    update_configs
    create_basic_models
    test_installation
    show_usage
    
    log_success "所有设置完成！"
}

# 运行主函数
main "$@"
