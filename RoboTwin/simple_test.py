#!/usr/bin/env python3
"""
简化的RoboTwin功能测试脚本 - 不需要渲染
"""

import sys
import os
import time
sys.path.append('.')

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    try:
        import sapien.core as sapien
        print("✓ SAPIEN导入成功")
        
        import envs
        print("✓ 环境模块导入成功")
        
        import torch
        print(f"✓ PyTorch导入成功 (版本: {torch.__version__})")
        
        from envs.robot.planner import CuroboPlanner
        print("✓ CuroboPlanner导入成功")
        
        import numpy as np
        print(f"✓ NumPy导入成功 (版本: {np.__version__})")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assets():
    """测试资源文件"""
    print("\n🔍 测试资源文件...")
    try:
        # 检查关键资源文件
        assets_path = "./assets"
        
        # 检查杂乱物体
        messy_objects_path = os.path.join(assets_path, "messy_objects")
        if os.path.exists(messy_objects_path):
            categories = [d for d in os.listdir(messy_objects_path) 
                         if os.path.isdir(os.path.join(messy_objects_path, d))]
            print(f"✓ 杂乱物体资源: {len(categories)} 个类别")
        
        # 检查背景纹理
        bg_path = os.path.join(assets_path, "background_texture")
        if os.path.exists(bg_path):
            seen_bg = os.path.join(bg_path, "seen")
            unseen_bg = os.path.join(bg_path, "unseen")
            seen_count = len(os.listdir(seen_bg)) if os.path.exists(seen_bg) else 0
            unseen_count = len(os.listdir(unseen_bg)) if os.path.exists(unseen_bg) else 0
            print(f"✓ 背景纹理: seen={seen_count}, unseen={unseen_count}")
        
        # 检查机器人实体
        entities_path = os.path.join(assets_path, "embodiments")
        if os.path.exists(entities_path):
            robots = [d for d in os.listdir(entities_path) 
                     if os.path.isdir(os.path.join(entities_path, d))]
            print(f"✓ 机器人实体: {robots}")
        
        return True
        
    except Exception as e:
        print(f"✗ 资源文件检查失败: {e}")
        return False

def test_task_modules():
    """测试任务模块导入"""
    print("\n🔍 测试任务模块...")
    
    # 定义所有任务
    tasks = [
        "place_object_scale",
        "blocks_ranking_rgb", 
        "blocks_stack_three",
        "dual_shoes_place",
        "place_phone_stand",
        "put_bottles_dustbin"
    ]
    
    success_count = 0
    
    for task_name in tasks:
        try:
            print(f"  测试任务模块: {task_name}")
            
            # 动态导入任务模块
            module_name = f"envs.{task_name}"
            module = __import__(module_name, fromlist=[task_name])
            task_class = getattr(module, task_name)
            
            print(f"    ✓ {task_name} 模块导入成功")
            success_count += 1
            
        except Exception as e:
            print(f"    ✗ {task_name} 失败: {e}")
    
    print(f"\n任务模块测试结果: {success_count}/{len(tasks)} 成功")
    return success_count >= len(tasks) // 2  # 至少一半成功

def test_configuration_files():
    """测试配置文件"""
    print("\n🔍 测试配置文件...")
    try:
        import yaml
        
        # 检查任务配置文件
        task_config_dir = "./task_config"
        if os.path.exists(task_config_dir):
            config_files = [f for f in os.listdir(task_config_dir) if f.endswith('.yml')]
            print(f"✓ 任务配置文件: {len(config_files)} 个")
            
            # 测试读取一个配置文件
            if config_files:
                sample_config = os.path.join(task_config_dir, config_files[0])
                with open(sample_config, 'r', encoding='utf-8') as f:
                    config = yaml.load(f.read(), Loader=yaml.FullLoader)
                print(f"✓ 配置文件格式正确: {config_files[0]}")
        
        # 检查实体配置
        embodiment_config = "./task_config/_embodiment_config.yml"
        if os.path.exists(embodiment_config):
            with open(embodiment_config, 'r', encoding='utf-8') as f:
                embodiment_types = yaml.load(f.read(), Loader=yaml.FullLoader)
            print(f"✓ 实体配置: {list(embodiment_types.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置文件测试失败: {e}")
        return False

def test_dependencies():
    """测试关键依赖"""
    print("\n🔍 测试关键依赖...")
    try:
        # 测试关键库
        dependencies = [
            ('numpy', 'np'),
            ('torch', 'torch'),
            ('sapien', 'sapien.core'),
            ('trimesh', 'trimesh'),
            ('yaml', 'yaml'),
            ('gymnasium', 'gymnasium'),
            ('toppra', 'toppra'),
            ('transforms3d', 'transforms3d')
        ]
        
        success_count = 0
        for name, import_name in dependencies:
            try:
                __import__(import_name)
                print(f"  ✓ {name}")
                success_count += 1
            except ImportError:
                print(f"  ✗ {name}")
        
        print(f"\n依赖测试结果: {success_count}/{len(dependencies)} 成功")
        return success_count == len(dependencies)
        
    except Exception as e:
        print(f"✗ 依赖测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("🤖 RoboTwin 简化功能测试")
    print("=" * 50)
    
    start_time = time.time()
    
    tests = [
        ("基本导入", test_basic_imports),
        ("资源文件", test_assets),
        ("任务模块", test_task_modules),
        ("配置文件", test_configuration_files),
        ("关键依赖", test_dependencies)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    end_time = time.time()
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    print(f"⏱️  用时: {end_time - start_time:.2f} 秒")
    
    if passed == total:
        print("🎉 所有测试通过！RoboTwin环境配置正确")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，环境基本可用")
    else:
        print("❌ 多个测试失败，需要检查环境配置")
    
    print("=" * 50)
    
    return passed >= total * 0.8

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
