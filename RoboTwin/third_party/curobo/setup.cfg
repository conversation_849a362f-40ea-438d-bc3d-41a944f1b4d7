# Copyright (c) 2023, NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto. Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.

# Additional files that need to be included in the package distribution must be
# listed in the MANIFEST.in file.
#
# References:
# * https://newbedev.com/how-include-static-files-to-setuptools-python-package

[metadata]

# Configure specific project settings
name = nvidia_curobo
author = NVIDIA Seattle Robotics Lab
description = GPU robot motion toolkit containing cuda accelerated kinematics, IK, MPC, Global motion planning, and optimization solvers.
url = https://curobo.org
license = NVIDIA

# Configure general project settings
long_description = file: README.md
long_description_content_type = text/markdown
license_files = LICENSE

# List of classifiers can be found here:
# https://pypi.org/classifiers/
classifiers =
  License :: Other/Proprietary License
  Operating System :: OS Independent
  Intended Audience :: Developers
  Natural Language :: English
  Programming Language :: Python :: 3
  Topic :: Scientific/Engineering :: Robotics

# Change if running only on Windows, Mac or Linux (comma-separated)
platforms = any

[options]
install_requires =
  pybind11
  networkx
  numpy
  numpy-quaternion
  pyyaml
  setuptools_scm>=6.2
  torch>=1.10
  trimesh
  yourdfpy>=0.0.53
  warp-lang>=0.9.0
  scipy>=1.7.0
  tqdm
  wheel
  importlib_resources
  scikit-image

packages = find_namespace:
package_dir =
  = src
include_package_data = True

[options.packages.find]
where = src
exclude =
    tests

[options.extras_require]
ci =
  anybadge
  black
  build
  flake8
  flake8-docstrings
  flake8-isort
  pytest>6.2.5
  pytest-cov
  twine
  sphinx
  sphinx_rtd_theme
  graphviz>=0.20.1
  furo
  sphinx-copybutton


# this is only available in 3.8+
smooth =
  trajectory_smoothing @ https://github.com/balakumar-s/trajectory_smoothing/raw/main/dist/trajectory_smoothing-0.3-cp38-cp38-linux_x86_64.whl

usd =
  usd-core

dev =
  ipdb
  ipython
  black
  flake8
  flake8-docstrings
  flake8-isort
  pytest>6.2.5
  pytest-cov

isaacsim =
  tomli
  wheel
  ninja

doc =
  sphinx
  sphinx_rtd_theme
  graphviz>=0.20.1
  furo
  sphinx-copybutton

[options.entry_points]
# Add here console scripts like:
# console_scripts =
#     script_name = package.module:function

# NOTE (roflaherty): Flake8 doesn't support pyproject.toml configuration yet.
[flake8]
max-line-length = 99
docstring-convention = google
exclude = .git,build,deprecated,dist,venv
ignore =
  W503
  E203
  E731
