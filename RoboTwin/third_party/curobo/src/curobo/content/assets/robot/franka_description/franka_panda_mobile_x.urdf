<?xml version="1.0" ?>
<robot name="panda" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <link name="base_link"/>
  <link name="base_link_x"/>


<joint name="base_x" type="prismatic">
    <origin rpy="0 0 0.0" xyz="0 0 0.0"/>
    <parent link="base_link"/>
    <child link="base_link_x"/>
    <axis xyz="1 0 0"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-5.0" upper="5.0" velocity="1.0"/>
  </joint>

<joint name="base_fixed" type="fixed">
    <origin rpy="0 0 0.0" xyz="0 0 0.1"/>
    <parent link="base_link_x"/>
    <child link="panda_link0"/>
    <axis xyz="0 0 0"/>
  </joint>

  


  <link name="panda_link0">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link0.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link0.obj"/>
      </geometry>
    </collision>
    
  </link>
  <link name="panda_link1">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link1.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link1.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint1" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="0 0 0" xyz="0 0 0.333"/>
    <parent link="panda_link0"/>
    <child link="panda_link1"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <!--limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/-->
    <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
  </joint>
  <link name="panda_link2">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link2.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link2.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint2" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-1.7628" soft_upper_limit="1.7628"/>
    <origin rpy="-1.57079632679 0 0" xyz="0 0 0"/>
    <parent link="panda_link1"/>
    <child link="panda_link2"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-1.7628" upper="1.7628" velocity="2.1750"/>
  </joint>
  <link name="panda_link3">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link3.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link3.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint3" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="1.57079632679 0 0" xyz="0 -0.316 0"/>
    <parent link="panda_link2"/>
    <child link="panda_link3"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-2.8973" upper="2.8973" velocity="2.1750"/>
  </joint>
  <link name="panda_link4">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link4.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link4.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint4" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-3.0718" soft_upper_limit="-0.0698"/>
    <origin rpy="1.57079632679 0 0" xyz="0.0825 0 0"/>
    <parent link="panda_link3"/>
    <child link="panda_link4"/>
    <axis xyz="0 0 1"/>    
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-3.0718" upper="-0.0698" velocity="2.1750"/>
    <!-- something is weird with this joint limit config
    <dynamics damping="10.0"/>
    <limit effort="87" lower="-3.0" upper="0.087" velocity="2.1750"/>  -->
  </joint>
  <link name="panda_link5">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link5.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link5.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint5" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="-1.57079632679 0 0" xyz="-0.0825 0.384 0"/>
    <parent link="panda_link4"/>
    <child link="panda_link5"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
  </joint>
  <link name="panda_link6">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link6.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link6.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint6" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-0.0175" soft_upper_limit="3.7525"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0 0"/>
    <parent link="panda_link5"/>
    <child link="panda_link6"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="12" lower="0.5" upper="3.7525" velocity="2.6100"/>
    <!-- <dynamics damping="10.0"/>
    <limit effort="12" lower="-0.0873" upper="3.0" velocity="2.6100"/> -->
  </joint>
  <link name="panda_link7">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/link7.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/link7.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_joint7" type="revolute">
    <safety_controller k_position="100.0" k_velocity="40.0" soft_lower_limit="-2.8973" soft_upper_limit="2.8973"/>
    <origin rpy="1.57079632679 0 0" xyz="0.088 0 0"/>
    <parent link="panda_link6"/>
    <child link="panda_link7"/>
    <axis xyz="0 0 1"/>
    <dynamics damping="10.0"/>
    <limit effort="12" lower="-2.8973" upper="2.8973" velocity="2.6100"/>
  </joint>
  <link name="panda_link8"/>
  <joint name="panda_joint8" type="fixed">
    <origin rpy="0 0 0" xyz="0 0 0.107"/>
    <parent link="panda_link7"/>
    <child link="panda_link8"/>
    <axis xyz="0 0 0"/>
  </joint>
  <joint name="panda_hand_joint" type="fixed">
    <!--
    <parent link="panda_link8"/>
    -->
    <parent link="panda_link8"/>
    <child link="panda_hand"/>
    <origin rpy="0 0 -0.785398163397" xyz="0 0 0.0"/>
    <!--
    <origin rpy="0 0 -0.785398163397" xyz="0 0 0"/>
    -->
  </joint>
  <link name="panda_hand">
    <visual>
      <origin rpy="1.57 0.0 0.0" xyz="0 0 0.0"/>
      <!--origin rpy="-1.57 0.0 0.0" xyz="0 0 0.0"/-->
      <geometry>
        <mesh filename="meshes/visual/hand.obj"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0.0 0.0 0.0" xyz="0 0 0.0"/>
      <geometry>
        <!--box size="0.01 0.01 0.01"/-->
        <!--mesh filename="meshes/visual/hand.obj"/-->
        <mesh filename="meshes/collision/hand_gripper.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="ee_fixed_joint" type="fixed">
    <parent link="panda_hand"/>
    <child link="grasp_frame"/>
    <!--origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0922 0.0"/-->
    <!--origin rpy="-1.57 0.0 1.57" xyz="0.0 0. 0.0"/-->
    <origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0 0.11"/> 
  </joint>
  <link name="grasp_frame">
    <collision>
      <geometry>
        <box size="0.01 0.01 0.01"/>
      </geometry>

      <origin rpy="0 0 0" xyz="0.0 0 0"/>
    </collision>
  </link>
    <joint name="ee_fixed_t" type="fixed">
    <parent link="panda_hand"/>
    <child link="ee_link"/>
    <!--origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0922 0.0"/-->
    <origin rpy="0 0.0 0" xyz="0.0 0. 0.1"/>
    <!--origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0 0.1"/--> 
    </joint>
  <link name="ee_link"/>
  <link name="ee_grasp_frame"/>
  <joint name="ee_grasp_joint" type="fixed">
    <parent link="panda_hand"/>
    <child link="ee_grasp_frame"/>
    <!--origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0922 0.0"/-->
    <origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0 0.0"/> 
    <!--origin rpy="0.0 0.0 1.570796325" xyz="0.0 0.0 0.1"/--> 
  </joint>

 


  
    <link name="panda_leftfinger">
    <visual>
      <geometry>
        <mesh filename="meshes/visual/finger.dae"/>
      </geometry>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/collision/finger.obj"/>
      </geometry>
    </collision>
  </link>
  <link name="panda_rightfinger">
    <visual>
      <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/visual/finger.dae"/>
      </geometry>
    </visual>
    <collision>
      <origin rpy="0 0 3.14159265359" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/collision/finger.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="panda_finger_joint1" type="fixed">
    <parent link="panda_hand"/>
    <child link="panda_leftfinger"/>
    <origin rpy="0 0 0" xyz="0 0.04 0.0584"/>
  </joint>
  <joint name="panda_finger_joint2" type="fixed">
    <parent link="panda_hand"/>
    <child link="panda_rightfinger"/>
    <origin rpy="0 0 0" xyz="0 -0.04 0.0584"/>
  </joint>

</robot>


