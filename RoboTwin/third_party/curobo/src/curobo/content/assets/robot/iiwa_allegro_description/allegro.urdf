<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from lula_kuka_allegro.urdf.xacro   | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="kuka_allegro" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <!-- ======================== BASE PARAMS ========================= -->
  <!-- ======================== FINGER PARAMS ======================== -->
  <!-- full height from joint to tip. when used,
       the radius of the finger tip sphere will be subtracted
       and one fixed link will be added for the tip. -->
  <!-- ========================= THUMB PARAMS ========================= -->
  <!-- ========================= LIMITS ========================= -->
  <!-- ============================================================================= -->
  <!-- BASE -->
  <link name="allegro_mount">
    <inertial>
      <mass value="0.05"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1e-4" ixy="0" ixz="0" iyy="1e-4" iyz="0" izz="1e-4"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/mounts/allegro_mount.obj"/>
      </geometry>
      <material name="color_j7"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/mounts/allegro_mount.obj"/>
      </geometry>
    </collision>
  </link>
  <joint name="allegro_mount_joint" type="fixed">
    <origin rpy="0 -1.5708 0.785398" xyz="-0.008219 -0.02063 0.08086"/>
    <parent link="allegro_mount"/>
    <child link="palm_link"/>
  </joint>
  <link name="palm_link">
    <inertial>
      <mass value="0.4154"/>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <inertia ixx="1e-4" ixy="0" ixz="0" iyy="1e-4" iyz="0" izz="1e-4"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/base_link.obj"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0 "/>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/base_link.obj"/>
      </geometry>
    </collision>
  </link>
  
  <gazebo reference="palm_link">
    <material value="Gazebo/Grey"/>
  </gazebo>
  <link name="index_link_0">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.005"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_1">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.005"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_2">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.05"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="index_link_3">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.11"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0. 0.5 0. 1"/>
      </material>
    </visual>
  </link>
  <link name="index_biotac_tip">
    <inertial>
      <mass value="0.04"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
  </link>
  <link name="middle_link_0">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_1">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_2">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_link_3">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>
  <link name="middle_biotac_tip">
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
  </link>
-->
  <link name="ring_link_0">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_1">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_2">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/primary_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="ring_link_3">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>

  <link name="ring_biotac_tip">
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
  </link>
  <link name="thumb_link_0">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_base.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_base.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_1">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_proximal.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_proximal.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_2">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_medial.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/allegro/thumb_medial.obj"/>
      </geometry>
      <material name="Grey">
        <color rgba="0.2 0.2 0.2 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_link_3">
    <collision>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor_thumb.obj"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
    <visual>
      <geometry>
        <mesh filename="kuka_allegro_description/meshes/biotac/biotac_sensor_thumb.obj"/>
      </geometry>
      <material name="Green">
        <color rgba="0 0.5 0 1"/>
      </material>
    </visual>
  </link>
  <link name="thumb_biotac_tip">
    <inertial>
      <mass value="0.1"/>
      <inertia ixx="5.1458e-5" ixy="0" ixz="0" iyy="5.1458e-5" iyz="0" izz="6.125e-5"/>
    </inertial>
  </link>

  <joint name="index_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 -0.0872638888889" xyz="0.0514302 -0.03632 -0.0113"/>
    <parent link="palm_link"/>
    <child link="index_link_0"/>
    <dynamics damping="0.025" friction="0.035"/>
  </joint>
  <joint name="index_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="index_link_0"/>
    <child link="index_link_1"/>
    <dynamics damping="0.025" friction="0.035"/>
  </joint>
  <joint name="index_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="index_link_1"/>
    <child link="index_link_2"/>
    <dynamics damping="0.025" friction="0.035"/>
  </joint>
  <joint name="index_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="index_link_2"/>
    <child link="index_link_3"/>
    <dynamics damping="0.025" friction="0.035"/>
  </joint>
  <joint name="middle_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0" xyz="0.0537375 0.0087771 -0.0113"/>
    <parent link="palm_link"/>
    <child link="middle_link_0"/>
    <dynamics friction="0.035"/>
  </joint>

  <joint name="middle_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="middle_link_0"/>
    <child link="middle_link_1"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="middle_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="middle_link_1"/>
    <child link="middle_link_2"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="middle_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="middle_link_2"/>
    <child link="middle_link_3"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="middle_biotac_tip_joint" type="fixed">
    <origin rpy="0 0 0.436319444444 " xyz="0.055 0.015 0"/>
    <parent link="middle_link_3"/>
    <child link="middle_biotac_tip"/>
  </joint>

  <joint name="ring_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.558488888889" upper="0.558488888889" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0.0872638888889" xyz="0.0514302 0.0538749 -0.0113"/>
    <parent link="palm_link"/>
    <child link="ring_link_0"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="ring_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 1.57075" xyz="0.0 0.0 0.0"/>
    <parent link="ring_link_0"/>
    <child link="ring_link_1"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="ring_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.054 0.0 0.0"/>
    <parent link="ring_link_1"/>
    <child link="ring_link_2"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="ring_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0384 0.0 0.0"/>
    <parent link="ring_link_2"/>
    <child link="ring_link_3"/>
    <dynamics friction="0.035"/>
  </joint>

  <joint name="ring_biotac_tip_joint" type="fixed">
    <origin rpy="0 0 0.436319444444 " xyz="0.055 0.015 0"/>
    <parent link="ring_link_3"/>
    <child link="ring_biotac_tip"/>
  </joint>

  <joint name="thumb_joint_0" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="0.279244444444" upper="1.57075" velocity="6.283"/>
    <origin rpy="-1.57075 -1.57075 1.48348611111" xyz="-0.0367482 -0.0081281 -0.0295"/>
    <parent link="palm_link"/>
    <child link="thumb_link_0"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_1" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.331602777778" upper="1.15188333333" velocity="6.283"/>
    <origin rpy="1.57075 0 0" xyz="0.005 0.0 0.0"/>
    <parent link="thumb_link_0"/>
    <child link="thumb_link_1"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_2" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.727825" velocity="6.283"/>
    <origin rpy="3.1415 -1.57075 0.0" xyz="0 0 0.0554"/>
    <parent link="thumb_link_1"/>
    <child link="thumb_link_2"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_joint_3" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="0.35" lower="-0.279244444444" upper="1.76273055556" velocity="6.283"/>
    <origin rpy="0.0 0.0 0.0" xyz="0.0514 0.0 0.0"/>
    <parent link="thumb_link_2"/>
    <child link="thumb_link_3"/>
    <dynamics friction="0.035"/>
  </joint>
  <joint name="thumb_biotac_tip_joint" type="fixed">
    <origin rpy="0 0 0.436319444444 " xyz="0.07 0.01 0"/>
    <parent link="thumb_link_3"/>
    <child link="thumb_biotac_tip"/>
  </joint>

</robot>
