<?xml version="1.0" ?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from tm12.urdf.xacro                | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="tm12">
  <gazebo reference="link_1">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="link_2">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="link_3">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="link_4">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="link_5">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo reference="link_6">
    <selfCollide>true</selfCollide>
  </gazebo>
  <gazebo>
    <plugin filename="libgazebo_ros_control.so" name="gazebo_ros_control">
      <robotNamespace>/</robotNamespace>
      <robotSimType>gazebo_ros_control/DefaultRobotHWSim</robotSimType>
    </plugin>
  </gazebo>
  <transmission name="trans_1">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="shoulder_1_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_1">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="trans_2">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="shoulder_2_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_2">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="trans_3">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="elbow_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_3">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="trans_4">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_1_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_4">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="trans_5">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_2_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_5">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <transmission name="trans_6">
    <type>transmission_interface/SimpleTransmission</type>
    <joint name="wrist_3_joint">
      <hardwareInterface>hardware_interface/PositionJointInterface</hardwareInterface>
    </joint>
    <actuator name="motor_6">
      <mechanicalReduction>1</mechanicalReduction>
    </actuator>
  </transmission>
  <material name="none">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="lightgrey">
    <color rgba="0.5 0.5 0.5 1.0"/>
  </material>
  <material name="grey">
    <color rgba="0.75 0.75 0.75 1.0"/>
  </material>
  <material name="white">
    <color rgba="0.95 0.95 0.95 1.0"/>
  </material>
  <material name="red">
    <color rgba="0.95 0.0 0.0 1.0"/>
  </material>
  <material name="orange">
    <color rgba="1.0 0.5 0.0 1.0"/>
  </material>
  <material name="yellow">
    <color rgba="1.0 1.0 0.0 1.0"/>
  </material>
  <material name="green">
    <color rgba="0.0 1.0 0.0 1.0"/>
  </material>
  <material name="blue">
    <color rgba="0.0 0.0 1.0 1.0"/>
  </material>
  <material name="indigo">
    <color rgba="0.3 0.3 0.6 1.0"/>
  </material>
  <material name="violet">
    <color rgba="0.6 0.0 1.0 1.0"/>
  </material>
  <material name="black">
    <color rgba="0.0 0.0 0.0 1.0"/>
  </material>
  <material name="darkolive">
    <color rgba="0.3 0.3 0.25 1.0"/>
  </material>
  <!--LinkDescription-->
  <link name="link_0">
    <visual>
      <geometry>
        <mesh filename="meshes/tm12/visual/tm12-base.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm12/collision/tm12-base_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <mass value="1.0"/>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <inertia ixx="0.00110833289" ixy="0.0" ixz="0.0" iyy="0.00110833289" iyz="0.0" izz="0.0018"/>
    </inertial>
  </link>
  <joint name="shoulder_1_joint" type="revolute">
    <parent link="link_0"/>
    <child link="link_1"/>
    <origin rpy="0.000000 -0.000000 0.000000" xyz="0.000000 0.000000 0.165200"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="353" lower="-4.71238898038469" upper="4.71238898038469" velocity="2.0943951023931953"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_1">
    <visual>
      <geometry>
        <mesh filename="meshes/tm12/visual/tmr_750w_01.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm12/collision/tmr_750w_01_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="7.6"/>
      <inertia ixx="0.020289334" ixy="0.000000" ixz="0.000000" iyy="0.020289334" iyz="0.000000" izz="0.021396270999999998"/>
    </inertial>
  </link>
  <joint name="shoulder_2_joint" type="revolute">
    <parent link="link_1"/>
    <child link="link_2"/>
    <origin rpy="-1.570796 -1.570796 0.000000" xyz="0.000000 0.000000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="353" lower="-3.141592653589793" upper="3.141592653589793" velocity="2.0943951023931953"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_2">
    <visual>
      <geometry>
        <mesh filename="meshes/tm12/visual/tm12-arm1.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm12/collision/tm12-arm1_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="14.0239"/>
      <inertia ixx="0.071505715" ixy="0.000000" ixz="0.000000" iyy="1.1758788999999998" iyz="0.000000" izz="1.2033932999999999"/>
    </inertial>
  </link>
  <joint name="elbow_joint" type="revolute">
    <parent link="link_2"/>
    <child link="link_3"/>
    <origin rpy="0.000000 -0.000000 0.000000" xyz="0.636100 0.000000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="157" lower="-2.897246558310587" upper="2.897246558310587" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_3">
    <visual>
      <geometry>
        <mesh filename="meshes/tm12/visual/tm12-arm2.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm12/collision/tm12-arm2_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="3.3577"/>
      <inertia ixx="0.009755469" ixy="0.000000" ixz="0.000000" iyy="0.16334719" iyz="0.000000" izz="0.16656678"/>
    </inertial>
  </link>
  <joint name="wrist_1_joint" type="revolute">
    <parent link="link_3"/>
    <child link="link_4"/>
    <origin rpy="0.000000 -0.000000 1.570796" xyz="0.557900 0.000000 -0.156300"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_4">
    <visual>
      <geometry>
        <mesh filename="meshes/tm5-900/visual/tmr_100w_01.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm5-900/collision/tmr_100w_01_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="1.576"/>
      <inertia ixx="0.002058405" ixy="0.000000" ixz="0.000000" iyy="0.0025630790000000002" iyz="0.000000" izz="0.00264321"/>
    </inertial>
  </link>
  <joint name="wrist_2_joint" type="revolute">
    <parent link="link_4"/>
    <child link="link_5"/>
    <origin rpy="1.570796 -0.000000 0.000000" xyz="0.000000 -0.106000 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-3.141592653589793" upper="3.141592653589793" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_5">
    <visual>
      <geometry>
        <mesh filename="meshes/tm5-900/visual/tmr_100w_02.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm5-900/collision/tmr_100w_02_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="1.576"/>
      <inertia ixx="0.002058405" ixy="0.000000" ixz="0.000000" iyy="0.0025630790000000002" iyz="0.000000" izz="0.00264321"/>
    </inertial>
  </link>
  <joint name="wrist_3_joint" type="revolute">
    <parent link="link_5"/>
    <child link="link_6"/>
    <origin rpy="1.570796 -0.000000 0.000000" xyz="0.000000 -0.113150 0.000000"/>
    <axis xyz="0 0 1"/>
    <!--limit-->
    <limit effort="54" lower="-4.71238898038469" upper="4.71238898038469" velocity="3.141592653589793"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="link_6">
    <visual>
      <geometry>
        <mesh filename="meshes/tm5-900/visual/tmr_ee.obj"/>
      </geometry>
      <material name="none"/>
    </visual>
    <collision>
      <geometry>
        <mesh filename="meshes/tm5-900/collision/tmr_ee_c.stl"/>
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
      <mass value="0.65"/>
      <inertia ixx="0.000774544" ixy="0.000000" ixz="0.000000" iyy="0.001383811" iyz="0.000000" izz="0.001559496"/>
    </inertial>
  </link>
  <joint name="flange_fixed_joint" type="fixed">
    <parent link="link_6"/>
    <child link="flange_link"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>
  <link name="flange_link"/>
  <link name="base"/>
  <joint name="base_fixed_joint" type="fixed">
    <parent link="base"/>
    <child link="link_0"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>
  <link name="tool0"/>
  <joint name="flange_link-tool0" type="fixed">
    <parent link="flange_link"/>
    <child link="tool0"/>
    <origin rpy="0.000000 0.000000 0.000000" xyz="0.000000 0.000000 0.000000"/>
  </joint>
  <!--LinkDescription-->
  <link name="world"/>
  <joint name="world_joint" type="fixed">
    <parent link="world"/>
    <child link="base"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
</robot>

