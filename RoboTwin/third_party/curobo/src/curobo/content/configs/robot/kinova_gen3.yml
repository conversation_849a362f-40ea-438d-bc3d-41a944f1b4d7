##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##
robot_cfg:
  kinematics:
    base_link: base_link
    collision_link_names:
    - base_link
    - shoulder_link
    - half_arm_1_link
    - half_arm_2_link
    - forearm_link
    - spherical_wrist_1_link
    - spherical_wrist_2_link
    - bracelet_link
    - robotiq_arg2f_base_link
    - left_outer_finger
    - left_inner_finger_pad
    - right_outer_finger
    - right_inner_finger_pad
    collision_sphere_buffer: 0.005
    collision_spheres: spheres/kinova_gen3.yml
    ee_link: tool_frame
    self_collision_buffer:
      base_link: 0.0
      forearm_link: 0.0
      half_arm_1_link: 0.0
      half_arm_2_link: 0.0
      shoulder_link: 0.0
      spherical_wrist_1_link : 0.0
      spherical_wrist_2_link : 0.0
      bracelet_link: 0.0
      robotiq_arg2f_base_link: 0.0
      left_outer_finger: 0.0
      left_inner_finger_pad: 0.0
      right_outer_finger: 0.0
      right_inner_finger_pad: 0.0
      left_outer_knuckle: 0.0
    self_collision_ignore: {
      base_link: [shoulder_link],
      shoulder_link: [half_arm_1_link],
      half_arm_1_link: [half_arm_2_link],
      half_arm_2_link: [forearm_link],
      forearm_link: [spherical_wrist_1_link],
      spherical_wrist_1_link: [spherical_wrist_2_link],
      spherical_wrist_2_link: [bracelet_link],
      bracelet_link: [robotiq_arg2f_base_link],
      robotiq_arg2f_base_link: [left_outer_finger, right_outer_finger, left_inner_finger_pad, right_inner_finger_pad],
      left_outer_finger: [right_outer_finger, right_inner_finger_pad, left_inner_finger_pad],
      right_outer_finger: [right_inner_finger_pad, left_inner_finger_pad],
      left_inner_finger_pad: [right_inner_finger_pad],

    }
    urdf_path: robot/kinova/kinova_gen3_7dof.urdf
    asset_root_path: robot/kinova
    mesh_link_names:
      - base_link
      - shoulder_link
      - half_arm_1_link
      - half_arm_2_link
      - forearm_link
      - spherical_wrist_1_link
      - spherical_wrist_2_link
      - bracelet_link
    cspace:
      joint_names: ['joint_1', 'joint_2', 'joint_3', 'joint_4', 'joint_5', 'joint_6', 'joint_7']
      cspace_distance_weight:
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0

      null_space_weight:
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      - 1.0
      retract_config:
      - 0.0
      - -0.8
      - 0.0
      - 1.5
      - 0.0
      - 0.4
      - 0.0
      max_acceleration: 25.0
      max_jerk: 500.0