##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##
collision_spheres:
  base_link:
      - "center": [0.0, 0.0, 0.1]
        "radius": 0.05
  
  shoulder_link:
      - "center": [0.0, 0.0, -0.1]
        "radius": 0.06
      - "center": [0.0, 0.0, -0.15]
        "radius": 0.05
  half_arm_1_link:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.055
      - "center": [0.0, -0.07, 0.0]
        "radius": 0.055
      - "center": [0.0, -0.15,0.0]
        "radius": 0.055
  half_arm_2_link:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.055
      - "center": [0.0, -0.0, -0.07]
        "radius": 0.055
      - "center": [0.0, -0.0,-0.15]
        "radius": 0.055
      - "center": [0.0, -0.0,-0.21]
        "radius": 0.055
  forearm_link:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.055
      - "center": [0.0, -0.07, -0.0]
        "radius": 0.055
      - "center": [0.0, -0.17,-0.0]
        "radius": 0.055
  spherical_wrist_1_link:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.055
      - "center": [0.0, -0.0, -0.085]
        "radius": 0.055
  spherical_wrist_2_link:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.05
      - "center": [0.0, -0.085, -0.0]
        "radius": 0.05
  bracelet_link:
      - "center": [0.0, -0.0, -0.05]
        "radius": 0.04
      - "center": [0.0, -0.05, -0.05]
        "radius": 0.04
  robotiq_arg2f_base_link:
      - "center": [0.0, -0.0, 0.04]
        "radius": 0.04
  left_outer_finger:
      - "center": [0.0, -0.01, 0.02]
        "radius": 0.03
  left_inner_finger_pad:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.01
  right_outer_finger:
      - "center": [0.0, -0.01, 0.02]
        "radius": 0.03
  right_inner_finger_pad:
      - "center": [0.0, -0.0, 0.0]
        "radius": 0.01
  
  
  