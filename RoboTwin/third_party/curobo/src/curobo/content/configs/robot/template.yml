##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

robot_cfg:
  kinematics:
    usd_path: "FILL_THIS"
    usd_robot_root: "/robot"
    isaac_usd_path: ""
    usd_flip_joints: {}
    usd_flip_joint_limits: []

    urdf_path: "FILL_THIS"
    asset_root_path: ""
    
    base_link: "FILL_THIS"
    ee_link: "FILL_THIS"
    link_names: null
    lock_joints: null
    extra_links: null

    
    collision_link_names: null # List[str]
    collision_spheres: null #
    collision_sphere_buffer: 0.005 # float or Dict[str, float]
    extra_collision_spheres: {}
    self_collision_ignore: {} # Dict[str, List[str]]
    self_collision_buffer: {} # Dict[str, float]

    use_global_cumul: True
    mesh_link_names: null # List[str]
    external_asset_path: null # Use this to add path for externally located assets/robot folder.

    cspace:
      joint_names: [] # List[str]
      retract_config: null # List[float]
      null_space_weight: null # List[str]
      cspace_distance_weight: null # List[str]
      max_jerk: 500.0
      max_acceleration: 15.0
