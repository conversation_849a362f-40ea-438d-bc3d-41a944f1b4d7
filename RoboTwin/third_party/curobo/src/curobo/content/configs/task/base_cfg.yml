##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##


world_collision_checker_cfg:
  cache: null
  checker_type: "PRIMITIVE"
  max_distance: 0.1


cost:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [0.0, 0.0]
    vec_convergence: [0.0, 0.00] # orientation, position
    terminal: False
  link_pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [0.0, 0.0]
    vec_convergence: [0.0, 0.00] # orientation, position
    terminal: False


  bound_cfg:
    weight: 000.0
    activation_distance: [0.0,0.0,0.0,0.0]

constraint:
  primitive_collision_cfg:
    weight: 2000.0
    use_sweep: False
    classify: True
  self_collision_cfg:
    weight: 1000.0
    classify: True
  bound_cfg:
    weight: [5000.0, 5000.0, 5000.0,5000.0]

    activation_distance: [0.0,0.0,0.0,0.0] # for position, velocity, acceleration and jerk


convergence:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [0.1,10.0] #[0.1, 100.0]
    vec_convergence: [0.0, 0.0] # orientation, position
    terminal: False
  link_pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [0.1, 10.0]
    vec_convergence: [0.0, 0.0] # orientation, position
    terminal: False
  cspace_cfg:
    weight: 1.0
    terminal: True
    run_weight: 0.0
    use_l2_kernel: True
  null_space_cfg:
    weight: 0.001
    terminal: True
    run_weight: 1.0
    use_l2_kernel: True

