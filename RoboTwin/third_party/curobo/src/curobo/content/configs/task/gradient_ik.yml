##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

model:
  horizon: 1
  state_filter_cfg:
    filter_coeff:
      position: 1.0
      velocity: 1.0
      acceleration: 0.0

    enable: False
  dt_traj_params:
    base_dt: 0.02
    base_ratio: 1.0
    max_dt: 0.25
  vel_scale: 1.0
  control_space: 'POSITION'
  teleport_mode: True
cost:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [2000,10000,20,40]
    vec_convergence: [0.0, 0.00]
    terminal: False
    use_metric: True
    run_weight: 1.0
  link_pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    weight: [2000,10000,20,40]
    vec_convergence: [0.00, 0.000]
    terminal: False
    use_metric: True
    run_weight: 1.0

  cspace_cfg:
    weight: 0.000

  bound_cfg:
    weight: 5000.0
    activation_distance: [0.001]
    null_space_weight: [0.001]
    use_l2_kernel: True

  primitive_collision_cfg:
    weight: 5000.0
    use_sweep: False
    classify: False
    activation_distance: 0.01

  self_collision_cfg:
    weight: 5000.0
    classify: False


lbfgs:
  n_iters: 100 #60
  inner_iters: 25
  cold_start_n_iters: null
  min_iters: null
  line_search_scale:  [0.1, 0.3, 0.7, 1.0]
  fixed_iters: True
  cost_convergence: 1e-11
  cost_delta_threshold: 1e-11 #0.0001
  cost_relative_threshold: 0.999
  epsilon: 0.01 #0.01 # used only in stable_mode
  history: 6
  horizon: 1
  use_cuda_graph: True
  n_problems: 1
  store_debug: False
  use_cuda_kernel: True
  use_shared_buffers_kernel: True
  stable_mode: True
  line_search_type: "approx_wolfe" #"wolfe"
  use_cuda_line_search_kernel: True
  use_cuda_update_best_kernel: True
  sync_cuda_time: True
  step_scale: 0.98
  use_coo_sparse: True
  last_best: 10
  debug_info:
    visual_traj       : null #'ee_pos_seq'


