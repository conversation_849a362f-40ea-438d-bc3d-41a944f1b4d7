##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

model:
  horizon: 30
  state_filter_cfg:
    filter_coeff:
      position: 0.0
      velocity: 0.0
      acceleration: 0.0
    enable: True
  dt_traj_params:
    base_dt: 0.05
    base_ratio: 0.5
    max_dt: 0.05
  vel_scale: 1.0
  control_space: 'POSITION'
  teleport_mode: False
  state_finite_difference_mode: "CENTRAL"
 
cost:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0] # orientation, position for all timesteps
    run_vec_weight: [1.00,1.00,1.00,1.0,1.0,1.0] # running weight orientation, position
    weight: [500,2000.0,10,10] #[150.0, 2000.0, 30, 40]
    vec_convergence: [0.0,0.0] #[0.001,0.0001] #[0.01, 0.001] # orientation, position
    terminal: True
    run_weight: 0.1
    use_metric: True
    
  cspace_cfg:
    weight: 00.0
    terminal: True
    run_weight: 1.0
    
  bound_cfg:
    weight: [50.0, 0.0,0.0,0.0]
    activation_distance: [0.1,0.1,0.1,0.1] # for position, velocity, acceleration and jerk
    smooth_weight: [0.0, 100.0, 0.0,0.0] # [vel, acc, jerk, alpha_vel, eta_position, eta_vel, eta_acc] 
    run_weight_velocity: 0.0
    run_weight_acceleration: 1.0
    run_weight_jerk: 1.0
  primitive_collision_cfg:
    weight: 500.0
    use_sweep: True
    sweep_steps: 4
    classify: False
    use_sweep_kernel: True
    use_speed_metric: False
    speed_dt: 0.1 # used only for speed metric
    activation_distance: 0.025
  self_collision_cfg:
    weight: 500.0
    classify: False

  null_space_cfg:
    weight: 0.1
    terminal: True
    run_weight: 1.0
    use_null_space: True
 
  stop_cfg:
    weight: 10.0 #50.0
    max_nlimit: 0.5 #0.2

lbfgs:
  n_iters: 500 #125 #@200 #250 #250 # 150 #25
  inner_iters: 25
  cold_start_n_iters: 500 #125 #200 #250 #$150 #25
  min_iters: 50
  line_search_scale: [0.01,0.3, 0.7,1.0] #[0.01,0.25,0.7, 1.0]  # [0.01, 0.8, 1.0] #
  fixed_iters: True
  cost_convergence: 0.01
  cost_delta_threshold: 0.0001
  epsilon: 0.01
  history: 15 #15
  use_cuda_graph: True
  n_problems: 1
  store_debug: False
  use_cuda_kernel: True
  stable_mode: True
  line_search_type: "approx_wolfe" #"strong_wolfe" #"strong_wolfe"
  use_cuda_line_search_kernel: True
  use_cuda_update_best_kernel: True
  sync_cuda_time: True
  use_temporal_smooth: False
  last_best: 10
  step_scale: 1.0
  use_coo_sparse: True
  debug_info:
    visual_traj       : null #'ee_pos_seq'
