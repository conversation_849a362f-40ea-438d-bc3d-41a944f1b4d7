##
## Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
##
## NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
## property and proprietary rights in and to this material, related
## documentation and any modifications thereto. Any use, reproduction,
## disclosure or distribution of this material and related documentation
## without an express license agreement from NVIDIA CORPORATION or
## its affiliates is strictly prohibited.
##

model:
  horizon: 32
  state_filter_cfg:
    filter_coeff:
      position: 1.0
      velocity: 1.0
      acceleration: 1.0
    enable: False
  dt_traj_params:
    base_dt: 0.15
    base_ratio: 1.0
    max_dt: 0.15
  vel_scale: 1.0
  control_space: 'POSITION'
  teleport_mode: False
  return_full_act_buffer: True
  state_finite_difference_mode: "CENTRAL"

cost:
  pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    run_vec_weight: [0.0,0.0,0.0,0.0,0.0,0.0] # running weight
    weight: [250.0, 5000.0, 20, 20]
    vec_convergence: [0.0,0.0,1000.0,1000.0]
    terminal: True
    run_weight: 0.0
    use_metric: True

  link_pose_cfg:
    vec_weight: [1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    run_vec_weight: [0.0,0.0,0.0,0.0,0.0,0.0] # running weight
    weight: [0.0, 5000.0, 40, 40]
    vec_convergence: [0.0,0.0,1000.0,1000.0]
    terminal: True
    run_weight: 0.00
    use_metric: True

  cspace_cfg:
    weight: 10000.0
    terminal: True
    run_weight: 0.0

  bound_cfg:
    weight: [0.1, 0.1,0.0,0.0]
    activation_distance: [0.0,0.0,0.0,0.0] #-#0.01
    #smooth_weight: [0.0,100.0,1.0,0.0]
    smooth_weight: [0.0,20.0,0.0,0.0]
    run_weight_velocity: 0.0
    run_weight_acceleration: 1.0
    run_weight_jerk: 1.0
    null_space_weight: [0.00]

  primitive_collision_cfg:
    weight: 5000.0
    use_sweep: False
    classify: False
    sweep_steps: 4
    use_sweep_kernel: False
    use_speed_metric: False
    speed_dt: 0.01  # used only for speed metric
    activation_distance: 0.025

  self_collision_cfg:
    weight: 500.0
    classify: False




mppi:
  init_cov          : 0.5
  gamma             : 1.0
  n_iters           : 2
  cold_start_n_iters: null
  step_size_mean    : 0.9
  step_size_cov     : 0.01
  beta              : 0.01
  alpha             : 1
  num_particles     : 25 # 100
  update_cov        : True
  cov_type          : "DIAG_A" #
  kappa             : 0.001
  null_act_frac     : 0.0
  sample_mode       : 'MEAN'
  base_action       : 'REPEAT'
  squash_fn         : 'CLAMP'
  n_problems            : 1
  use_cuda_graph    : True
  seed              : 0
  store_debug       : False
  random_mean       : True
  sample_per_problem: True
  sync_cuda_time    : False
  use_coo_sparse    : True
  sample_params:
    fixed_samples: True
    sample_ratio: {'halton':0.0, 'halton-knot':0.0, 'random':0.0, 'random-knot':0.0, "stomp": 1.0}
    seed: 25
    filter_coeffs: [0.3, 0.3, 0.4]
    n_knots: 5
  debug_info:
    visual_traj       : null #'ee_pos_seq'


