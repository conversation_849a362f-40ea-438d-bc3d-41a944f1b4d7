#
# Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
#

"""
This module contains functions for geometric processing such as pointcloud processing, analytic
signed distance computation for the environment, and also signed distance computation between robot
and the environment. These functions can be used for robot self collision checking and also for
robot environment collision checking.
"""
