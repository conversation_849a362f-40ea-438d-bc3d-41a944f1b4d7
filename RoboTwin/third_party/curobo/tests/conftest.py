#
# Copyright (c) 2023 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
#
# NVIDIA CORPORATION, its affiliates and licensors retain all intellectual
# property and proprietary rights in and to this material, related
# documentation and any modifications thereto. Any use, reproduction,
# disclosure or distribution of this material and related documentation
# without an express license agreement from NVIDIA CORPORATION or
# its affiliates is strictly prohibited.
#

# Standard Library
import os

os.environ["CUROBO_TORCH_COMPILE_DISABLE"] = str(1)
os.environ["CUROBO_USE_LRU_CACHE"] = str(1)
os.environ["CUROBO_TORCH_CUDA_GRAPH_RESET"] = str(0)
